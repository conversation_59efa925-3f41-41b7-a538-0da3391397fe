# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ReadPal is a Text-to-Speech (TTS) system with automatic reading order detection for PowerPoint and PDF documents. It uses advanced OCR, layout analysis, and multilingual speech synthesis.

## Build/Development Commands

```bash
# Install dependencies using UV
uv add <package_name>  # Add new dependencies
uv sync  # Sync all dependencies from pyproject.toml

# Run Python scripts with UV
uv run main.py  # Run main TTS pipeline for PPT/PDF conversion
uv run gputest.py  # Run GPU capability test
uv run example/voice_translator.py --url <youtube_url> --target_language <lang>  # Run voice translator

# If dependencies fail (e.g., NumPy version conflicts)
uv sync --reinstall  # Reinstall all dependencies
```

## Architecture & Key Components

### Core Pipeline (main.py)
- **Document Processing**: PPT→PDF→Images flow using LibreOffice and pdf2image
- **OCR Engine**: PaddleOCR v4 with multi-language support (zh, en, ja)
- **Layout Analysis**: Reading order detection, column detection, title recognition
- **TTS Engine**: Coqui TTS with XTTS-v2 model for multilingual synthesis

### Voice Translation (example/voice_translator.py)
- Downloads YouTube videos via yt-dlp
- Transcribes with OpenAI Whisper
- Translates text between languages
- Synthesizes speech in target language
- Merges audio with original video

## Code Style Guidelines

### Python Development
- Follow PEP 8 naming conventions
- Use type hints for function parameters and return values
- Handle exceptions with specific try/except blocks
- Document functions with clear docstrings
- Use UTF-8 encoding for all file operations

### GPU/Performance Considerations
- Check GPU availability before using CUDA operations
- Use batch processing for multiple images/documents
- Monitor memory usage with large PDF files
- Optimize image resolution for OCR accuracy vs speed

### Multi-language Handling
- Default to Chinese ('ch') for OCR unless specified
- Support language codes: 'ch' (Chinese), 'en' (English), 'japan' (Japanese)
- Use appropriate TTS models for target language
- Handle mixed-language documents appropriately

## Common Development Tasks

### Adding New Dependencies
```bash
# Add a new package
uv add <package_name>

# Add a development dependency
uv add --dev <package_name>
```

### Adding New Language Support
1. Update OCR language parameter in `process_ppt_to_speech()`
2. Ensure TTS model supports the language
3. Test with sample documents in that language

### Debugging OCR Issues
- Save intermediate images with bounding boxes using `save_debug=True`
- Check OCR confidence scores in results
- Adjust image preprocessing parameters if needed

### Optimizing Reading Order
- Tune spatial clustering thresholds for text grouping
- Adjust column detection parameters for specific layouts
- Test with various document formats (single/multi-column, tables)

## Dependencies & Environment

- **Platform**: Ubuntu/Linux with CUDA support
- **Python**: >=3.10
- **Package Manager**: UV (modern Python package installer)
- **Key Libraries**: OpenCV, PyTorch, PaddleOCR, Coqui TTS
- **Hardware**: NVIDIA GPU recommended for performance
- **Special**: Uses Jetpack 6 optimized wheels for ARM64 architecture

## Testing Guidelines

When adding new features:
- Test with various document formats (PPT, PDF)
- Verify multi-language support
- Check GPU memory usage with large files
- Validate audio quality output
- Test reading order accuracy on complex layouts

## Running Tests
```bash
# Run all tests (when test suite is implemented)
uv run pytest

# Run specific test file
uv run pytest tests/test_ocr.py
```