# ReadPal - Intelligent Document-to-Speech System

ReadPal is a Text-to-Speech (TTS) system with automatic reading order detection for PowerPoint, PDF, and image files. It uses advanced OCR and layout analysis to intelligently extract text in the correct reading order, then converts it to natural-sounding speech.

## Features

- **Multi-format Support**: Process PPT, PPTX, PDF, and common image formats (PNG, JPG, JPEG, BMP, TIFF, WEBP)
- **Intelligent Reading Order**: Automatically detects columns, titles, and proper text flow
- **Multi-language Support**: OCR and TTS in English, Chinese, and Japanese
- **Multiple TTS Engines**: MeloTTS (primary) and gTTS (fallback) with automatic engine selection
- **MP3 Output**: High-quality audio output in MP3 format
- **GPU Acceleration**: CUDA support for faster processing
- **Voice Cloning**: Optional speaker voice customization

## Installation

### System Requirements

- Ubuntu 18.04+ (tested on 20.04, 22.04)
- Python 3.10+
- ~2GB disk space for models
- 4GB+ RAM recommended
- NVIDIA GPU (optional, for acceleration)

### Install Dependencies

```bash
# 1. System packages (install first to avoid MeCab issues)
sudo apt update
sudo apt install libreoffice poppler-utils ffmpeg
sudo apt-get install mecab libmecab-dev mecab-ipadic-utf8

# 2. Core Python packages (using UV)
uv add paddlepaddle paddleocr pdf2image opencv-python pydub gtts

# 3. MeloTTS from GitHub (after system dependencies)
uv add git+https://github.com/myshell-ai/MeloTTS.git
uv add mecab-python3 unidic-lite

# 4. Download Japanese dictionary
uv run python -m unidic download

# 5. Test MeloTTS installation
uv run python -c "from melo.api import TTS; print('✅ MeloTTS ready')"

# 6. Optional: GPU acceleration (NVIDIA only)
uv add paddlepaddle-gpu
```

## Usage

### Command Line Interface

The main script now accepts command-line arguments for flexible usage:

```bash
# Basic usage with default Chinese language
uv run python main.py <file_path>

# Specify language (en, zh, ja)
uv run python main.py <file_path> <language>

# With optional parameters
uv run python main.py <file_path> <language> --speaker-voice <voice_file> --output-dir <output_directory>

# Examples:
# Process a PowerPoint file in English
uv run python main.py presentation.pptx en

# Process a PDF file in Japanese
uv run python main.py document.pdf ja

# Process an image file in Chinese (default)
uv run python main.py screenshot.png

# Process with custom voice cloning
uv run python main.py document.pdf en --speaker-voice reference_voice.wav

# Process with custom output directory
uv run python main.py presentation.pptx zh --output-dir custom_output/
```

### Python API

```python
from main import UbuntuDocumentTTSPipeline

# Initialize the pipeline
pipeline = UbuntuDocumentTTSPipeline()

# Process a PowerPoint file
pipeline.process_ppt_to_speech("presentation.pptx", language="en")

# Process a PDF file  
pipeline.process_ppt_to_speech("document.pdf", language="zh")

# Process with custom parameters
results = pipeline.process_document(
    file_path="document.pdf",
    output_audio_path="output/speech.mp3",
    language="ja",
    speaker_voice="reference.wav"
)
```

## Architecture

### Processing Pipeline

1. **Document Conversion**: PPT/PPTX � PDF � Images
2. **OCR Processing**: PaddleOCR v4 extracts text with bounding boxes
3. **Layout Analysis**: Intelligent reading order detection
4. **Text Combination**: Smart text block merging
5. **Speech Synthesis**: Multi-engine TTS with fallback support

### Key Components

- **OCR Engine**: PaddleOCR v4 with multi-language support
- **Layout Analyzer**: Custom algorithm for reading order detection
- **TTS Engines**: 
  - MeloTTS (primary: high quality, multilingual, GPU acceleration)
  - gTTS (fallback: Google TTS, wide language support)

## Advanced Features

### Voice Cloning

Provide a reference WAV file to clone the speaker's voice:

```bash
python main.py document.pdf en --speaker-voice speaker_reference.wav
```

### Batch Processing

Process multiple files in a directory:

```bash
for file in documents/*.pdf; do
    uv run python main.py "$file" en
done
```

### Custom OCR Settings

Modify OCR parameters in the code for specific document types:

```python
# In main.py, adjust OCR initialization
self.ocr = PaddleOCR(
    use_angle_cls=True,
    lang='ch',  # or 'en', 'japan'
    use_gpu=True,
    det_db_thresh=0.3,  # Adjust detection threshold
    det_db_box_thresh=0.5  # Adjust box threshold
)
```

## Troubleshooting

### Common Issues

1. **MeloTTS Installation Fails**: Install system dependencies first, then Python packages:
   ```bash
   # Install MeCab system dependencies FIRST
   sudo apt-get install mecab libmecab-dev mecab-ipadic-utf8
   # Then install Python packages
   uv add git+https://github.com/myshell-ai/MeloTTS.git
   uv add mecab-python3 unidic-lite
   uv run python -m unidic download
   ```

2. **MeCab Initialization Error**: Ensure Japanese dictionary is downloaded:
   ```bash
   uv run python -m unidic download
   uv run python -c "import MeCab; print('MeCab ready')"
   ```

3. **LibreOffice Conversion Fails**: Ensure LibreOffice is properly installed:
   ```bash
   libreoffice --version
   ```

4. **GPU Not Detected**: Check CUDA installation:
   ```bash
   uv run python -c "import torch; print(torch.cuda.is_available())"
   ```

5. **Dependency Conflicts**: Remove conflicting TTS package if librosa version issues occur:
   ```bash
   uv remove TTS  # Remove Coqui TTS if conflicts with MeloTTS
   ```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- PaddleOCR for powerful OCR capabilities
- Coqui TTS for open-source speech synthesis
- MeloTTS for high-quality multilingual TTS
- The open-source community for various supporting libraries