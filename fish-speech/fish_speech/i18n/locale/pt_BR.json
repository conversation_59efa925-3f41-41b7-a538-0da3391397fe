{"5 to 10 seconds of reference audio, useful for specifying speaker.": "5 a 10 segundos de áudio de referência, útil para especificar o orador.", "A text-to-speech model based on VQ-GAN and Llama developed by [Fish Audio](https://fish.audio).": "Um modelo de texto para fala baseado em VQ-GAN e Llama desenvolvido por [Fish Audio](https://fish.audio).", "Accumulate Gradient Batches": "Acumular Lotes de Gradiente", "Add to Processing Area": "Adicionar à Área de Processamento", "Added path successfully!": "Caminho adicionado com sucesso!", "Advanced Config": "Configuração Avançada", "Base LLAMA Model": "Modelo LLAMA Base", "Batch Inference": "Inferência em Lote", "Batch Size": "Tamanho do Lote", "Changing with the Model Path": "Alterando com o Caminho do Modelo", "Compile Model": "Compilar <PERSON>", "Compile the model can significantly reduce the inference time, but will increase cold start time": "Compilar o modelo pode reduzir significativamente o tempo de inferência, mas aumentará a latência inicial", "Copy": "Copiar", "Data Preprocessing": "Pré-processamento de Dados", "Data Preprocessing Path": "Caminho de Pré-processamento de Dados", "Data Source": "Fonte de Dados", "Decoder Model Config": "Configuração do Modelo Decodificador", "Decoder Model Path": "Caminho do Modelo Decodificador", "Disabled": "Desativado", "Enable Initial Prompt": "Habilitar Prompt Inicial", "Enable Reference Audio": "Habilitar Áudio de Referência", "English": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Japanese": "<PERSON><PERSON><PERSON><PERSON>", "Chinese": "<PERSON><PERSON><PERSON>", "Portuguese": "Português", "Spanish": "Espanhol", "Error Message": "Mensagem de Erro", "Faster Whisper, Up to 5g GPU memory usage": "Faster <PERSON> (Usa até 5 GB de vRAM)", "File Preprocessing": "Pré-processamento de Arquivos", "Generate": "<PERSON><PERSON><PERSON>", "Generated Audio": "<PERSON><PERSON><PERSON>", "If there is no corresponding text for the audio, apply ASR for assistance, support .txt or .lab format": "Se não houver texto correspondente ao áudio, utilize o ASR para assistência (formatos .txt ou .lab)", "Infer interface is closed": "A interface de inferência foi fechada", "Inference Configuration": "Configuração de Inferência", "Inference Server Configuration": "Configuração do Servidor de Inferência", "Inference Server Error": "Erro do Servidor de Inferência", "Inferring interface is launched at {}": "A interface de inferência foi iniciada em {}", "Initial Learning Rate": "Taxa de Aprendizagem Inicial", "Initial Prompt": "Prompt Inicial", "Initial prompt can provide contextual or vocabulary-specific guidance to the model.": "O prompt inicial pode fornecer orientação contextual ou específica de vocabulário para o modelo.", "Input Audio & Source Path for Transcription": "Entrada de Áudio/Caminho de Origem para Transcrição", "Input Text": "Texto de Entrada", "Invalid path: {}": "<PERSON><PERSON><PERSON>: {}", "It is recommended to use CUDA, if you have low configuration, use CPU": "Para GPUs Nvidia é recomendado usar CUDA. Se não tiver uma GPU Nvidia, use CPU", "Iterative Prompt Length, 0 means off": "Comprimento do Prompt Iterativo (0 = desativado)", "LLAMA Configuration": "Configuração do LLAMA", "LLAMA Model Config": "Configuração do Modelo LLAMA", "LLAMA Model Path": "Caminho do Modelo LLAMA", "Labeling Device": "Dispositivo de Rotulagem", "LoRA Model to be merged": "Modelo LoRA para mesclagem", "Maximum Length per Sample": "Comprimento Máximo por Amostra", "Maximum Training Steps": "Etapas Máximas de Treinamento", "Maximum tokens per batch, 0 means no limit": "Número máximo de tokens por lote, 0 significa sem limite", "Merge": "Mesclar", "Merge LoRA": "Mesclar LoRA", "Merge successfully": "Mesclado com sucesso", "Model Output Path": "<PERSON><PERSON><PERSON> de Saída do Modelo", "Model Quantization": "Quantização do Modelo", "Model Size": "Tamanho do Modelo", "Move": "Mover", "Move files successfully": "Arquivos movidos com sucesso", "No audio generated, please check the input text.": "<PERSON><PERSON><PERSON> gera<PERSON>, verifique o texto de entrada.", "No selected options": "Nenhuma opção selecionada", "Normalization Result Preview (Currently Only Chinese)": "Pré-visualização do Resultado da Normalização (Atualmente Apenas Chinês)", "Number of Workers": "Número de Processos", "Open Inference Server": "<PERSON><PERSON><PERSON> Ser<PERSON>or de Inferência", "Open Labeler WebUI": "Abrir WebUI de Rotulagem", "Open Tensorboard": "Abrir Tensorboard", "Opened labeler in browser": "WebUI de rotulagem aberta no navegador", "Optional Label Language": "Idioma do Rótulo (Opcional)", "Optional online ver": "Versão online (opcional)", "Output Path": "<PERSON><PERSON><PERSON>í<PERSON>", "Path error, please check the model file exists in the corresponding path": "Erro de caminho, verifique se o arquivo do modelo existe no caminho correspondente", "Post-quantification Precision": "Precisão Pós-quantização", "Precision": "Precisão", "Probability of applying Speaker Condition": "Probabilidade de Aplicar Condição de Orador", "Put your text here.": "Insira seu texto aqui.", "Quantify": "Quantizar", "Quantify successfully": "Quantizado com sucesso", "Realtime Transform Text": "Transformar Texto em Tempo Real", "Reference Audio": "Áudio de Referência", "Reference Text": "Texto de Referência", "warning": "Aviso", "Pre-processing begins...": "O pré-processamento começou!", "Related code and weights are released under CC BY-NC-SA 4.0 License.": "O código relacionado e os pesos são licenciados sob a Licença CC BY-NC-SA 4.0.", "Remove Selected Data": "Remover Dados Selecionados", "Removed path successfully!": "Caminho removido com sucesso!", "Repetition Penalty": "Penalidade de Repetição", "Save model every n steps": "Salvar modelo a cada n etapas", "Select LLAMA ckpt": "Selecionar .ckpt do LLAMA", "Select source file processing method": "Escolha como processar o arquivo de origem", "Select the model to be trained (Depending on the Tab page you are on)": "Selecione o modelo para o treinamento (dependendo da aba em que você está)", "Selected: {}": "Selecionado: {}", "Speaker is identified by the folder name": "O orador é identificado pelo nome da pasta", "Start Training": "Iniciar <PERSON>", "Streaming Audio": "Áudio em Streaming", "Streaming Generate": "Geração em Streaming", "Tensorboard Host": "Host do Tensorboard", "Tensorboard Log Path": "<PERSON><PERSON><PERSON> de Log do Tensorboard", "Tensorboard Port": "Porta do Tensorboard", "Tensorboard interface is closed": "A interface do Tensorboard está fechada", "Tensorboard interface is launched at {}": "A interface do Tensorboard foi iniciada em {}", "Text Normalization": "Normalização de Texto", "Text is too long, please keep it under {} characters.": "O texto é muito longo. Mantenha-o com menos de {} caracteres.", "The lower the quantitative precision, the more the effectiveness may decrease, but the greater the efficiency will increase": "Quanto menor a precisão quantitativa, mais a efic<PERSON>cia pode diminuir, mas maior será o aumento da eficiência", "The path of the input folder on the left or the filelist. Whether checked or not, it will be used for subsequent training in this list.": "O caminho da pasta de entrada à esquerda ou a lista de arquivos. Independentemente de estar marcada ou não, ela será utilizada para o treinamento subsequente nesta lista.", "Training Configuration": "Configuração de Treinamento", "Training Error": "Erro de Treinamento", "Training stopped": "Treinamento interrompido!", "Type the path or select from the dropdown": "Digite o caminho ou selecione no menu suspenso", "Use LoRA": "Usar <PERSON>", "Use LoRA can save GPU memory, but may reduce the quality of the model": "O uso de LoRAs pode economizar memória da GPU, mas também pode reduzir a qualidade", "Use filelist": "Usar lista de arquivos", "VQGAN Configuration": "Configuração do VQGAN", "View the status of the preprocessing folder (use the slider to control the depth of the tree)": "Visualizar o status da pasta de pré-processamento (use o controle deslizante para controlar a profundidade da árvore)", "We are not responsible for any misuse of the model, please consider your local laws and regulations before using it.": "Não nos responsabilizamos por qualquer uso indevido do modelo. Por favor, considere as leis e regulamentações locais antes de usá-lo.", "WebUI Host": "Host da Web<PERSON>", "WebUI Port": "Porta da WebUI", "Whisper Model": "<PERSON><PERSON>", "You can find the source code [here](https://github.com/fishaudio/fish-speech) and models [here](https://huggingface.co/fishaudio/fish-speech-1).": "Você pode encontrar o código fonte [aqui](https://github.com/fishaudio/fish-speech) e os modelos [aqui](https://huggingface.co/fishaudio/fish-speech-1).", "auto": "automático", "bf16-true is recommended for 30+ series GPU, 16-mixed is recommended for 10+ series GPU": "bf16-true é recomendado para GPUs da série 30+, 16-mixed é recomendado para GPUs da série 10+", "latest": "mais recente", "new": "novo", "This audio introduces the basic concepts and applications of artificial intelligence and machine learning.": "Este áudio introduz os conceitos básicos e aplicações de inteligência artificial e aprendizado de máquina.", "You don't need to train this model!": "Não é necessário treinar este modelo!", "Yes": "<PERSON>m", "No": "Não", "version:": "versão:", "author:": "autor:"}