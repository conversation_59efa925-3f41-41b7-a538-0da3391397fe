"""
ReadPal TTS Pipeline: Free Open-Source Document-to-Speech Processing
===================================================================

This implementation uses only free, open-source tools available on Ubuntu:
- LibreOffice (usually pre-installed) for PPT processing
- pdf2image + poppler for PDF processing
- PaddleOCR for multilingual text recognition
- Open-source layout analysis
- MeloTTS (primary) and gTTS (fallback) for speech synthesis

No commercial licenses or Windows-specific tools required!
"""

import os
import cv2
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import time
import warnings

# Suppress PaddlePaddle warnings about /proc/self/io and logging initialization
# These are harmless warnings from PaddlePaddle trying to access system performance metrics
os.environ['GLOG_minloglevel'] = '2'  # Suppress INFO and WARNING logs from glog
warnings.filterwarnings('ignore', category=UserWarning)

# Core libraries
from pdf2image import convert_from_path
from pdf2image.exceptions import PDFInfoNotInstalledError
from paddleocr import PaddleOCR
import numpy as np

# PyTorch for device detection
import torch

# Primary TTS engine: MeloTTS
try:
    from melo.api import TTS as MeloTTS  # type: ignore
    HAS_MELO = True
except ImportError:
    HAS_MELO = False
    MeloTTS = None  # Define as None to avoid undefined variable errors

# Fallback TTS engine: gTTS
try:
    from gtts import gTTS
    HAS_GTTS = True
except ImportError:
    HAS_GTTS = False

# Fish Speech TTS client
try:
    from fish_speech_client import FishSpeechClient
    HAS_FISH_SPEECH = True
except ImportError:
    HAS_FISH_SPEECH = False

# Audio processing
try:
    from pydub import AudioSegment
    HAS_PYDUB = True
except ImportError:
    HAS_PYDUB = False
    print("Warning: pydub not available. Install with: pip install pydub")

class UbuntuDocumentTTSPipeline:
    """
    Complete TTS pipeline for Ubuntu using only free, open-source tools
    """
    
    def __init__(self, tts_engine: str = "auto", fish_speech_url: str = "http://0.0.0.0:8080", fish_timeout: float = 120.0):
        """
        Initialize TTS pipeline
        
        Args:
            tts_engine: TTS engine to use ('fish', 'melo', 'gtts', 'auto')
                       'auto' will use fallback order: fish -> melo -> gtts
            fish_speech_url: URL for Fish Speech API server (when using fish engine)
            fish_timeout: Timeout in seconds for Fish Speech API requests (default: 120.0)
        """
        # Check dependencies
        self._check_dependencies()
        
        # Language mapping for PaddleOCR (ISO 639-1 to PaddleOCR format)
        self.ocr_lang_map = {
            'en': 'en',
            'zh': 'ch',
            'ja': 'japan',  # PaddleOCR uses 'japan' instead of 'ja'
            'ch': 'ch',  # Support both 'ch' and 'zh' for Chinese
            'japan': 'japan'  # Support legacy 'japan' code
        }
        
        # Don't initialize OCR here - will be done per-language in extract_text_with_ocr
        self.ocr = None
        self.current_ocr_lang = None
        
        # Configure TTS engine
        self.tts_engine = tts_engine.lower()
        self.fish_speech_url = fish_speech_url
        self.fish_timeout = fish_timeout
        self.fish_client = None
        
        # Validate TTS engine choice and initialize if needed
        self._setup_tts_engine()
    
    def _check_dependencies(self):
        """Check if required system dependencies are available"""
        dependencies = {
            'libreoffice': 'LibreOffice for PPT processing',
            'pdftoppm': 'Poppler for PDF processing'
        }
        
        missing = []
        for cmd, desc in dependencies.items():
            if not shutil.which(cmd):
                missing.append(f"❌ {cmd} - {desc}")
        
        if missing:
            print("Missing dependencies:")
            for dep in missing:
                print(f"  {dep}")
            print("\nInstall with:")
            print("sudo apt update")
            print("sudo apt install libreoffice poppler-utils")
            print("pip install pdf2image paddleocr TTS opencv-python")
        else:
            print("✅ All system dependencies found!")
    
    def _setup_tts_engine(self):
        """Setup TTS engine based on user configuration"""
        valid_engines = ['fish', 'melo', 'gtts', 'auto']
        
        if self.tts_engine not in valid_engines:
            print(f"⚠️ Invalid TTS engine '{self.tts_engine}'. Using 'auto' fallback.")
            self.tts_engine = 'auto'
        
        # Initialize Fish Speech client if needed
        if self.tts_engine == 'fish' or self.tts_engine == 'auto':
            if HAS_FISH_SPEECH:
                try:
                    self.fish_client = FishSpeechClient(base_url=self.fish_speech_url, timeout=self.fish_timeout)
                    if self.fish_client.test_connection():
                        print(f"✅ Fish Speech API connected at {self.fish_speech_url}")
                    else:
                        print(f"⚠️ Fish Speech API not reachable at {self.fish_speech_url}")
                        if self.tts_engine == 'fish':
                            print("🔄 Consider using 'auto' mode for fallback support")
                except Exception as e:
                    print(f"⚠️ Failed to initialize Fish Speech client: {e}")
                    self.fish_client = None
            else:
                print("⚠️ Fish Speech client not available (missing dependencies)")
                if self.tts_engine == 'fish':
                    print("🔄 Install with: uv add ormsgpack requests")
        
        # Print TTS engine status
        available_engines = []
        if HAS_FISH_SPEECH and self.fish_client:
            available_engines.append("Fish Speech")
        if HAS_MELO:
            available_engines.append("MeloTTS")
        if HAS_GTTS:
            available_engines.append("gTTS")
        
        print(f"🎵 TTS Engine: {self.tts_engine}")
        print(f"📊 Available engines: {', '.join(available_engines) if available_engines else 'None'}")
    
    def _check_gpu(self) -> bool:
        """Check if GPU is available"""
        try:
            import paddle
            return paddle.device.is_compiled_with_cuda()
        except:
            return False
    
    
    def ppt_to_pdf_libreoffice(self, 
                              ppt_path: str, 
                              output_dir: str) -> str:
        """
        Convert PPT/PPTX to PDF using LibreOffice (free, pre-installed on Ubuntu)
        
        Args:
            ppt_path: Path to PPT/PPTX file
            output_dir: Directory to save PDF
            
        Returns:
            Path to generated PDF file
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Get absolute paths (required for LibreOffice)
        ppt_path = os.path.abspath(ppt_path)
        output_dir = os.path.abspath(output_dir)
        
        print(f"🔄 Converting PPT to PDF: {os.path.basename(ppt_path)}")
        
        try:
            # LibreOffice command for headless conversion
            cmd = [
                'libreoffice',
                '--headless',           # No GUI
                '--invisible',          # Completely invisible
                '--convert-to', 'pdf',  # Output format
                '--outdir', output_dir, # Output directory
                ppt_path                # Input file
            ]
            
            # Execute conversion
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2 minute timeout
            )
            
            if result.returncode != 0:
                raise Exception(f"LibreOffice conversion failed: {result.stderr}")
            
            # Find generated PDF file
            ppt_name = Path(ppt_path).stem
            pdf_path = os.path.join(output_dir, f"{ppt_name}.pdf")
            
            if not os.path.exists(pdf_path):
                # Sometimes LibreOffice changes the filename slightly
                pdf_files = [f for f in os.listdir(output_dir) if f.endswith('.pdf')]
                if pdf_files:
                    pdf_path = os.path.join(output_dir, pdf_files[0])
                else:
                    raise Exception("PDF file not generated")
            
            print(f"✅ PPT converted to PDF: {pdf_path}")
            return pdf_path
            
        except subprocess.TimeoutExpired:
            raise Exception("LibreOffice conversion timed out (>2 minutes)")
        except Exception as e:
            raise Exception(f"PPT to PDF conversion failed: {str(e)}")
    
    
    def pdf_to_images(self, 
                     pdf_path: str, 
                     dpi: int = 300,
                     output_folder: Optional[str] = None) -> List[str]:
        """
        Convert PDF pages to images using pdf2image + poppler
        """
        if output_folder is None:
            output_folder = tempfile.mkdtemp()
        os.makedirs(output_folder, exist_ok=True)
        
        print(f"🔄 Converting PDF to images: {os.path.basename(pdf_path)}")
        
        try:
            # Convert with optimized settings for OCR
            images = convert_from_path(
                pdf_path,
                dpi=dpi,                        # High DPI for better OCR
                output_folder=output_folder,    # Save to disk to avoid memory issues
                fmt='JPEG',                     # JPEG for smaller files
                thread_count=4,                 # Multi-threading
                grayscale=False,                # Keep colors for layout detection
            )
            
            # Get image paths
            image_paths = []
            for i, image in enumerate(images):
                image_path = os.path.join(output_folder, f"page_{i+1:03d}.jpg")
                if not os.path.exists(image_path):
                    image.save(image_path, 'JPEG', quality=95)
                image_paths.append(image_path)
            
            print(f"✅ Converted {len(image_paths)} pages to images")
            return image_paths
            
        except PDFInfoNotInstalledError:
            raise Exception(
                "Poppler not installed!\n"
                "Install with: sudo apt install poppler-utils"
            )
        except Exception as e:
            raise Exception(f"PDF to images conversion failed: {str(e)}")
    
    def extract_text_with_ocr(self, image_path: str, language: str = 'zh') -> List[Dict]:
        """
        Extract text using PaddleOCR with layout information
        
        Args:
            image_path: Path to the image file
            language: Language code ('en', 'zh', 'ja')
        """
        print(f"🔍 Extracting text from: {os.path.basename(image_path)}")
        
        # Map language code to PaddleOCR format
        ocr_lang = self.ocr_lang_map.get(language, 'ch')
        
        # Initialize or reinitialize OCR if language changed
        if self.ocr is None or self.current_ocr_lang != ocr_lang:
            print(f"🌐 Initializing OCR for language: {language} (OCR code: {ocr_lang})")
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang=ocr_lang,
                use_gpu=self._check_gpu(),
                show_log=False
            )
            self.current_ocr_lang = ocr_lang
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise Exception(f"Could not load image: {image_path}")
        
        # Run OCR
        ocr_results = self.ocr.ocr(image, cls=True)
        
        text_blocks = []
        if ocr_results and ocr_results[0]:
            for line_data in ocr_results[0]:
                coords = line_data[0]  # 4 corner points
                text_info = line_data[1]  # (text, confidence)
                
                # Calculate bounding box
                xs = [point[0] for point in coords]
                ys = [point[1] for point in coords]
                bbox = [min(xs), min(ys), max(xs), max(ys)]
                
                text_blocks.append({
                    'text': text_info[0],
                    'confidence': text_info[1],
                    'bbox': bbox,
                    'coordinates': coords,
                    'area': (max(xs) - min(xs)) * (max(ys) - min(ys))
                })
        
        print(f"📝 Found {len(text_blocks)} text blocks")
        return text_blocks
    
    def detect_reading_order(self, text_blocks: List[Dict], image_shape: Tuple[int, int]) -> List[Dict]:
        """
        Simple but effective reading order detection for presentations
        """
        if not text_blocks:
            return []
        
        height, width = image_shape[:2]
        
        # Filter out very small text (likely noise)
        min_area = width * height * 0.0001  # 0.01% of image area
        text_blocks = [b for b in text_blocks if b['area'] > min_area]
        
        # Sort by layout type
        if self._is_title_slide(text_blocks, width, height):
            return self._order_title_slide(text_blocks)
        elif self._is_two_column_layout(text_blocks, width):
            return self._order_two_column(text_blocks, width)
        else:
            return self._order_standard_reading(text_blocks)
    
    def _is_title_slide(self, text_blocks: List[Dict], width: int, height: int) -> bool:
        """Detect if this is a title slide"""
        if len(text_blocks) <= 3:
            # Check if there's a large text block in the upper portion
            upper_blocks = [b for b in text_blocks if b['bbox'][1] < height * 0.4]
            if upper_blocks:
                largest_block = max(upper_blocks, key=lambda x: x['area'])
                return largest_block['area'] > width * height * 0.02
        return False
    
    def _is_two_column_layout(self, text_blocks: List[Dict], width: int) -> bool:
        """Detect two-column layout"""
        if len(text_blocks) < 4:
            return False
        
        left_blocks = [b for b in text_blocks if b['bbox'][2] < width * 0.5]
        right_blocks = [b for b in text_blocks if b['bbox'][0] > width * 0.5]
        
        return len(left_blocks) >= 2 and len(right_blocks) >= 2
    
    def _order_title_slide(self, text_blocks: List[Dict]) -> List[Dict]:
        """Order text for title slides (top to bottom)"""
        return sorted(text_blocks, key=lambda x: x['bbox'][1])
    
    def _order_two_column(self, text_blocks: List[Dict], width: int) -> List[Dict]:
        """Order text for two-column layout"""
        left_column = [b for b in text_blocks if b['bbox'][2] < width * 0.5]
        right_column = [b for b in text_blocks if b['bbox'][0] > width * 0.5]
        
        # Sort each column vertically
        left_column.sort(key=lambda x: x['bbox'][1])
        right_column.sort(key=lambda x: x['bbox'][1])
        
        # Combine: left first, then right
        return left_column + right_column
    
    def _order_standard_reading(self, text_blocks: List[Dict]) -> List[Dict]:
        """Standard reading order with improved grouping"""
        # Group text blocks by approximate line (Y-position)
        lines = []
        tolerance = 20  # pixels
        
        for block in text_blocks:
            y_center = (block['bbox'][1] + block['bbox'][3]) / 2
            
            # Find existing line to add to
            added_to_line = False
            for line in lines:
                line_y = sum((b['bbox'][1] + b['bbox'][3]) / 2 for b in line) / len(line)
                if abs(y_center - line_y) < tolerance:
                    line.append(block)
                    added_to_line = True
                    break
            
            # Create new line if needed
            if not added_to_line:
                lines.append([block])
        
        # Sort lines by Y position, blocks within lines by X position
        ordered_blocks = []
        for line in sorted(lines, key=lambda l: min(b['bbox'][1] for b in l)):
            line_blocks = sorted(line, key=lambda x: x['bbox'][0])
            ordered_blocks.extend(line_blocks)
        
        return ordered_blocks
    
    def process_document(self, 
                        file_path: str, 
                        output_audio_path: str,
                        language: str = "en",
                        speaker_voice: Optional[str] = None,
                        cleanup_temp: bool = True) -> Dict:
        """
        Complete pipeline: Document -> PDF -> Images -> OCR -> TTS
        """
        file_ext = Path(file_path).suffix.lower()
        start_time = time.time()
        
        # Create temporary directory
        temp_dir = tempfile.mkdtemp(prefix="tts_pipeline_")
        
        try:
            print(f"🚀 Starting TTS pipeline for: {os.path.basename(file_path)}")
            
            # Step 1: Convert to PDF (if PPT)
            if file_ext in ['.ppt', '.pptx']:
                pdf_path = self.ppt_to_pdf_libreoffice(file_path, temp_dir)
            elif file_ext == '.pdf':
                pdf_path = file_path
            else:
                raise Exception(f"Unsupported file format: {file_ext}")
            
            # Step 2: PDF to images
            image_paths = self.pdf_to_images(pdf_path, dpi=300, output_folder=temp_dir)
            
            # Step 3: Extract text from each page
            all_text_blocks = []
            for i, image_path in enumerate(image_paths):
                print(f"📄 Processing page {i+1}/{len(image_paths)}")
                
                # Extract text
                text_blocks = self.extract_text_with_ocr(image_path, language)
                
                # Detect reading order
                image = cv2.imread(image_path)
                ordered_blocks = self.detect_reading_order(text_blocks, image.shape)
                
                # Add page information
                for block in ordered_blocks:
                    block['page'] = i + 1
                
                all_text_blocks.extend(ordered_blocks)
            
            # Step 4: Combine text
            final_text = self._combine_text_blocks(all_text_blocks)
            
            # Step 5: Generate speech
            if final_text.strip():
                print("🎵 Generating speech...")
                self._generate_speech(final_text, output_audio_path, language, speaker_voice)
                print(f"✅ Audio generated: {output_audio_path}")
            else:
                print("⚠️ Skipping TTS (no text extracted)")
            
            # Results
            results = {
                'success': True,
                'file_path': file_path,
                'pages_processed': len(image_paths),
                'text_blocks_found': len(all_text_blocks),
                'processing_time': time.time() - start_time,
                'output_audio': output_audio_path,
                'text_preview': final_text[:200] + "..." if len(final_text) > 200 else final_text,
                'average_confidence': np.mean([b['confidence'] for b in all_text_blocks]) if all_text_blocks else 0
            }
            
            return results
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path,
                'processing_time': time.time() - start_time
            }
        
        finally:
            # Cleanup temporary files
            if cleanup_temp:
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
    
    def _combine_text_blocks(self, text_blocks: List[Dict]) -> str:
        """Combine text blocks with proper spacing"""
        if not text_blocks:
            return ""
        
        # Group by page
        pages = {}
        for block in text_blocks:
            page = block['page']
            if page not in pages:
                pages[page] = []
            pages[page].append(block)
        
        # Combine text
        combined_text = []
        for page_num in sorted(pages.keys()):
            page_blocks = pages[page_num]
            
            # Join text with spaces, add periods for sentences
            page_text = []
            for block in page_blocks:
                text = block['text'].strip()
                if text:
                    # Add period if text doesn't end with punctuation
                    if not text[-1] in '.!?':
                        text += '.'
                    page_text.append(text)
            
            if page_text:
                combined_text.append(' '.join(page_text))
        
        return '\n\n'.join(combined_text)
    
    def _generate_speech(self, text: str, output_path: str, language: str, speaker_voice: Optional[str]):
        """Generate speech using configured TTS engine with fallback support"""
        # Prepare output directory
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        if self.tts_engine == 'fish':
            # Use Fish Speech only
            return self._generate_speech_fish(text, output_path, language)
        elif self.tts_engine == 'melo':
            # Use MeloTTS only
            return self._generate_speech_melo(text, output_path, language)
        elif self.tts_engine == 'gtts':
            # Use gTTS only
            return self._generate_speech_gtts(text, output_path, language)
        else:
            # Auto fallback mode: fish -> melo -> gtts
            # Try Fish Speech first (best for cross-language text)
            if HAS_FISH_SPEECH and self.fish_client:
                try:
                    print(f"🐟 Generating speech with Fish Speech in {language}...")
                    return self._generate_speech_fish(text, output_path, language)
                except Exception as fish_error:
                    print(f"⚠️ Fish Speech failed: {fish_error}")
                    print("🔄 Falling back to MeloTTS...")
            
            # Try MeloTTS second (high-quality multilingual TTS)
            if HAS_MELO:
                try:
                    print(f"🎵 Generating speech with MeloTTS in {language}...")
                    return self._generate_speech_melo(text, output_path, language)
                except Exception as melo_error:
                    print(f"⚠️ MeloTTS failed: {melo_error}")
                    print("🔄 Falling back to gTTS...")
            
            # Try gTTS as final fallback
            if HAS_GTTS:
                try:
                    print(f"🎵 Generating speech with gTTS in {language}...")
                    return self._generate_speech_gtts(text, output_path, language)
                except Exception as gtts_error:
                    print(f"⚠️ gTTS failed: {gtts_error}")
            
            print(f"❌ All TTS engines failed. No audio generated.")
    
    def _generate_speech_melo(self, text: str, output_path: str, language: str) -> str:
        """Generate speech using MeloTTS"""
        # Map language codes to MeloTTS format
        melo_lang_map = {
            "en": "EN",
            "fr": "FR", 
            "es": "ES",
            "zh": "ZH", # MeloTTS uses 'ZH' for Chinese
            "ja": "JP",
            "ko": "KR"
        }
        
        if language not in melo_lang_map:
            raise ValueError(f"MeloTTS does not support language {language}")
        
        melo_language = melo_lang_map[language]
        
        # Initialize MeloTTS model with CUDA if available
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device for MeloTTS: {device}")
        model = MeloTTS(language=melo_language, device=device)
        speaker_ids = model.hps.data.spk2id
        
        # Select appropriate speaker
        speaker_id = None
        if melo_language == "EN":
            speaker_id = speaker_ids.get('EN-Default', list(speaker_ids.values())[0])
        else:
            # For other languages, get the default speaker
            available_speakers = list(speaker_ids.keys())
            for spk in available_speakers:
                if melo_language in spk:
                    speaker_id = speaker_ids[spk]
                    break
            
            if speaker_id is None and available_speakers:
                speaker_id = speaker_ids[available_speakers[0]]
        
        # Generate temporary WAV file
        temp_wav = output_path.replace('.mp3', '_temp.wav')
        
        # Generate speech
        speed = 1.0
        model.tts_to_file(text, speaker_id, temp_wav, speed=speed)
        
        # Verify the generated file
        if not os.path.exists(temp_wav) or os.path.getsize(temp_wav) == 0:
            raise RuntimeError(f"Generated audio file is empty or missing: {temp_wav}")
        
        # Convert WAV to MP3 if pydub is available
        if HAS_PYDUB:
            try:
                audio = AudioSegment.from_wav(temp_wav)
                audio.export(output_path, format="mp3", bitrate="128k")
                os.remove(temp_wav)
                print(f"✅ MeloTTS audio converted to MP3: {output_path}")
            except Exception as conv_error:
                print(f"⚠️ MP3 conversion failed: {conv_error}")
                # Keep WAV file if MP3 conversion fails
                shutil.move(temp_wav, output_path.replace('.mp3', '.wav'))
                print(f"✅ MeloTTS audio saved as WAV: {output_path.replace('.mp3', '.wav')}")
        else:
            # If no pydub, keep as WAV
            shutil.move(temp_wav, output_path.replace('.mp3', '.wav'))
            print(f"✅ MeloTTS audio saved as WAV: {output_path.replace('.mp3', '.wav')}")
        
        return output_path
    
    def _generate_speech_gtts(self, text: str, output_path: str, language: str) -> str:
        """Generate speech using gTTS"""
        # Map target language to gTTS format
        gtts_lang_map = {
            "en": "en",
            "fr": "fr",
            "es": "es",
            "de": "de",
            "it": "it",
            "ja": "ja",
            "ko": "ko",
            "zh": "zh-CN",
            "ru": "ru",
            "pt": "pt",
            "nl": "nl",
            "tr": "tr",
            "pl": "pl",
            "ar": "ar",
        }
        
        gtts_language = gtts_lang_map.get(language, "en")
        
        # Generate MP3 with gTTS
        temp_mp3 = output_path.replace('.mp3', '_temp.mp3')
        tts = gTTS(text=text, lang=gtts_language, slow=False)
        tts.save(temp_mp3)
        
        # Verify the MP3 file
        if not os.path.exists(temp_mp3) or os.path.getsize(temp_mp3) == 0:
            raise RuntimeError(f"Generated MP3 file is empty or missing: {temp_mp3}")
        
        # Convert to final output format
        if output_path.endswith('.mp3'):
            shutil.move(temp_mp3, output_path)
            print(f"✅ gTTS audio saved as MP3: {output_path}")
        else:
            # Convert MP3 to WAV if needed
            if HAS_PYDUB:
                try:
                    audio = AudioSegment.from_mp3(temp_mp3)
                    wav_path = output_path.replace('.mp3', '.wav')
                    audio.export(wav_path, format="wav")
                    os.remove(temp_mp3)
                    print(f"✅ gTTS audio converted to WAV: {wav_path}")
                except Exception as conv_error:
                    print(f"⚠️ WAV conversion failed: {conv_error}")
                    shutil.move(temp_mp3, output_path)
                    print(f"✅ gTTS audio saved as MP3: {output_path}")
            else:
                shutil.move(temp_mp3, output_path)
                print(f"✅ gTTS audio saved as MP3: {output_path}")
        
        return output_path
    
    def _generate_speech_fish(self, text: str, output_path: str, language: str) -> str:
        """Generate speech using Fish Speech API"""
        if not self.fish_client:
            raise RuntimeError("Fish Speech client not initialized")
        
        # Fish Speech handles cross-language text well, so we don't need strict language mapping
        # Use reference audio and text for consistent voice generation
        success = self.fish_client.generate_speech(
            text=text,
            output_path=output_path,
            reference_audio_paths=["reference_audio/reference_audio.wav"],
            reference_text_paths=["reference_audio/reference_audio.txt"],
            language=language,
            format="wav",  # Always generate WAV for consistency
            temperature=0.8,
            top_p=0.8,
            repetition_penalty=1.1,
            max_new_tokens=2048,
            chunk_length=600,
            seed=42  # Fixed seed for consistent voice generation
        )
        
        if not success:
            raise RuntimeError("Fish Speech synthesis failed")
        
        # Verify the generated file
        if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
            raise RuntimeError(f"Generated audio file is empty or missing: {output_path}")
        
        print(f"✅ Fish Speech audio saved: {output_path}")
        return output_path


# Ubuntu Installation Helper
def install_ubuntu_dependencies():
    """Show installation commands for Ubuntu"""
    print("""
🔧 Ubuntu Installation Guide for ReadPal TTS Pipeline
====================================================

1. Update system and install system dependencies:
   sudo apt update
   sudo apt install libreoffice poppler-utils ffmpeg
   sudo apt-get install mecab libmecab-dev mecab-ipadic-utf8

2. Install core Python packages using UV:
   uv add paddlepaddle paddleocr pdf2image opencv-python pydub gtts

3. Install MeloTTS from GitHub (after system dependencies):
   uv add git+https://github.com/myshell-ai/MeloTTS.git
   uv add mecab-python3 unidic-lite

4. Download Japanese dictionary:
   uv run python -m unidic download

5. Test MeloTTS installation:
   uv run python -c "from melo.api import TTS; print('✅ MeloTTS imported successfully')"

6. For GPU acceleration (NVIDIA GPU):
   uv add paddlepaddle-gpu

7. Final verification test:
   uv run python -c "import pdf2image, paddleocr, melo, gtts; print('✅ All packages ready')"

📋 System Requirements:
- Ubuntu 18.04+ (tested on 20.04, 22.04)  
- Python 3.10+
- ~3GB disk space for models (MeloTTS + PaddleOCR + UniDic)
- 4GB+ RAM recommended
- NVIDIA GPU optional for acceleration

🎯 Quick Test Commands:
libreoffice --version
pdftoppm -h
ffmpeg -version
mecab --version

🚀 Usage Examples:
uv run python main.py image.png ja          # Japanese text from image
uv run python main.py document.pdf en       # English text from PDF  
uv run python main.py presentation.pptx zh  # Chinese text from PowerPoint

📊 Supported Languages: English (en), Chinese (zh), Japanese (ja)
📁 Supported Formats: PPT, PPTX, PDF, PNG, JPG, JPEG, BMP, TIFF, WEBP

⚠️  Important: Install system dependencies (step 1) before Python packages to avoid MeCab issues
""")


def main():
    import argparse
    
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="ReadPal TTS: Convert PPT/PDF/Image files to speech with intelligent reading order detection"
    )
    parser.add_argument(
        "file_path",
        help="Path to the input file (PPT, PDF, or image file)"
    )
    parser.add_argument(
        "language",
        choices=["en", "zh", "ja"],
        default="zh",
        nargs="?",
        help="Language for OCR and TTS (default: zh)"
    )
    parser.add_argument(
        "--speaker-voice",
        type=str,
        default=None,
        help="Optional path to speaker voice WAV file for voice cloning"
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="datafiles/voice",
        help="Output directory for generated audio files (default: datafiles/voice)"
    )
    parser.add_argument(
        "--tts-engine",
        type=str,
        choices=["fish", "melo", "gtts", "auto"],
        default="auto",
        help="TTS engine to use: fish (Fish Speech API), melo (MeloTTS), gtts (Google TTS), auto (fallback) (default: auto)"
    )
    parser.add_argument(
        "--fish-url",
        type=str,
        default="http://0.0.0.0:8080",
        help="Fish Speech API server URL (default: http://0.0.0.0:8080)"
    )
    parser.add_argument(
        "--fish-timeout",
        type=float,
        default=120.0,
        help="Fish Speech API timeout in seconds (default: 120.0)"
    )
    
    # Parse arguments
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.file_path):
        print(f"❌ Error: File not found: {args.file_path}")
        return 1
    
    # Check if running on Ubuntu
    try:
        with open('/etc/os-release', 'r') as f:
            os_info = f.read()
            if 'ubuntu' not in os_info.lower():
                print("⚠️ This script is optimized for Ubuntu. Other Linux distros may need adjustments.")
    except:
        pass
    
    # Initialize pipeline
    print(f"\n🚀 Initializing ReadPal TTS Pipeline...")
    print(f"📄 Input file: {args.file_path}")
    print(f"🌐 Language: {args.language}")
    print(f"📁 Output directory: {args.output_dir}")
    print(f"🎵 TTS Engine: {args.tts_engine}")
    if args.tts_engine in ['fish', 'auto']:
        print(f"🐟 Fish Speech URL: {args.fish_url}")
    
    try:
        pipeline = UbuntuDocumentTTSPipeline(
            tts_engine=args.tts_engine,
            fish_speech_url=args.fish_url,
            fish_timeout=args.fish_timeout
        )
        
        print("\n" + "="*60)
        print("🎯 Ubuntu TTS Pipeline Ready!")
        print("="*60)
        
        # Determine file type and process accordingly
        file_ext = os.path.splitext(args.file_path)[1].lower()
        
        # Generate output filename based on input file
        input_name = os.path.splitext(os.path.basename(args.file_path))[0]
        output_audio_path = os.path.join(args.output_dir, f"{input_name}_speech.mp3")
        
        if file_ext in ['.ppt', '.pptx', '.pdf']:
            # Process PPT/PDF files using the main document processing method
            results = pipeline.process_document(
                file_path=args.file_path,
                output_audio_path=output_audio_path,
                language=args.language,
                speaker_voice=args.speaker_voice
            )
            
            if results['success']:
                print(f"\n✅ Processing completed successfully!")
                print(f"📊 Results:")
                print(f"   • Pages processed: {results['pages_processed']}")
                print(f"   • Text blocks found: {results['text_blocks_found']}")
                print(f"   • Processing time: {results['processing_time']:.2f} seconds")
                print(f"   • Average confidence: {results['average_confidence']:.2f}")
                print(f"   • Audio file: {results['output_audio']}")
                print(f"   • Text preview: {results['text_preview']}")
            else:
                print(f"❌ Processing failed: {results['error']}")
                return 1
                
        elif file_ext == '.txt':
            # Process text files directly
            print(f"📝 Processing text file directly...")
            
            # Read text from file
            try:
                with open(args.file_path, 'r', encoding='utf-8') as f:
                    final_text = f.read().strip()
                
                if final_text:
                    print(f"📄 Text loaded from file ({len(final_text)} characters)")
                    
                    # Generate speech directly from text
                    pipeline._generate_speech(
                        final_text, 
                        output_audio_path, 
                        args.language, 
                        args.speaker_voice
                    )
                    print(f"✅ Audio generated: {output_audio_path}")
                    print(f"📝 Text content: {final_text[:1000]}..." if len(final_text) > 1000 else f"📝 Text content: {final_text}")
                else:
                    print("⚠️ Text file is empty")
                    return 1
                    
            except UnicodeDecodeError:
                print("❌ Error: Could not decode text file. Please ensure it's UTF-8 encoded.")
                return 1
            except Exception as e:
                print(f"❌ Error reading text file: {e}")
                return 1
                
        elif file_ext in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp']:
            # Process image files directly
            print(f"🖼️ Processing image file directly...")
            
            # Extract text from image
            text_blocks = pipeline.extract_text_with_ocr(
                args.file_path,
                language=args.language
            )
            
            if not text_blocks:
                print("⚠️ No text found in image")
                return 1
            
            # Add page information for single image (required by _combine_text_blocks)
            for block in text_blocks:
                block['page'] = 1
            
            # Combine text blocks
            final_text = pipeline._combine_text_blocks(text_blocks)
            
            # Generate speech
            if final_text.strip():
                print("\n🎵 Generating speech...")
                pipeline._generate_speech(
                    final_text, 
                    output_audio_path, 
                    args.language, 
                    args.speaker_voice
                )
                print(f"✅ Audio generated: {output_audio_path}")
                print(f"📝 Text extracted: {final_text[:200]}..." if len(final_text) > 200 else f"📝 Text extracted: {final_text}")
            else:
                print("⚠️ No text found in image")
                return 1
        else:
            print(f"❌ Unsupported file type: {file_ext}")
            print("Supported formats: TXT, PPT, PPTX, PDF, PNG, JPG, JPEG, BMP, TIFF, WEBP")
            return 1
        
        print("\n✅ Processing complete!")
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n🔧 Installation required:")
        install_ubuntu_dependencies()
        return 1


# Usage Example
if __name__ == "__main__":
    import sys
    sys.exit(main())