"""
FishSpeechClient - HTTP client wrapper for Fish Speech API server

This client provides a clean interface to communicate with the Fish Speech API server
without importing any Fish Speech libraries directly. Uses HTTP requests with ormsgpack
serialization for communication.

Usage:
    client = FishSpeechClient(base_url="http://0.0.0.0:8080")
    success = client.generate_speech("Hello world", "output.wav", language="en")
"""

from typing import Optional, List, Tuple, Dict, Any
import logging
import time
from pathlib import Path
import io

try:
    import ormsgpack
    HAS_ORMSGPACK = True
except ImportError:
    HAS_ORMSGPACK = False
    ormsgpack = None

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    requests = None


class FishSpeechClient:
    """HTTP client for Fish Speech API server communication"""
    
    def __init__(
        self,
        base_url: str = "http://0.0.0.0:8080",
        api_key: Optional[str] = None,
        timeout: float = 60.0
    ):
        """
        Initialize Fish Speech client
        
        Args:
            base_url: Base URL of Fish Speech API server
            api_key: Optional API key for authentication
            timeout: Request timeout in seconds
        """
        if not HAS_REQUESTS:
            raise ImportError("requests library is required. Install with: uv add requests")
        if not HAS_ORMSGPACK:
            raise ImportError("ormsgpack library is required. Install with: uv add ormsgpack")
            
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.session = requests.Session()
        
        if self.api_key:
            self.session.headers.update({
                "authorization": f"Bearer {self.api_key}"
            })
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _create_tts_request(
        self,
        text: str,
        reference_audios: Optional[List[bytes]] = None,
        reference_texts: Optional[List[str]] = None,
        reference_id: Optional[str] = None,
        format: str = "wav",
        max_new_tokens: int = 1024,
        chunk_length: int = 300,
        top_p: float = 0.8,
        repetition_penalty: float = 1.1,
        temperature: float = 0.8,
        streaming: bool = False,
        use_memory_cache: str = "off",
        seed: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create TTS request data structure"""
        
        # Handle reference audio/text pairs
        references = []
        if reference_id is None:
            ref_audios = reference_audios or []
            ref_texts = reference_texts or []
            
            # Ensure both lists have same length
            max_len = max(len(ref_audios), len(ref_texts))
            while len(ref_audios) < max_len:
                ref_audios.append(b"")
            while len(ref_texts) < max_len:
                ref_texts.append("")
            
            for ref_text, ref_audio in zip(ref_texts, ref_audios):
                references.append({
                    "audio": ref_audio,
                    "text": ref_text
                })
        
        return {
            "text": text,
            "references": references,
            "reference_id": reference_id,
            "format": format,
            "max_new_tokens": max_new_tokens,
            "chunk_length": chunk_length,
            "top_p": top_p,
            "repetition_penalty": repetition_penalty,
            "temperature": temperature,
            "streaming": streaming,
            "use_memory_cache": use_memory_cache,
            "seed": seed,
        }

    def _read_audio_file(self, audio_path: str) -> bytes:
        """Read audio file and return as bytes"""
        try:
            with open(audio_path, 'rb') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Failed to read audio file {audio_path}: {e}")
            return b""

    def _read_text_file(self, text_path: str) -> str:
        """Read text file and return content"""
        try:
            with open(text_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            self.logger.error(f"Failed to read text file {text_path}: {e}")
            return ""

    def generate_speech(
        self,
        text: str,
        output_path: str,
        reference_audio_paths: Optional[List[str]] = None,
        reference_text_paths: Optional[List[str]] = None,
        reference_id: Optional[str] = None,
        language: str = "auto",
        format: str = "wav",
        temperature: float = 0.8,
        top_p: float = 0.8,
        repetition_penalty: float = 1.1,
        max_new_tokens: int = 2048,
        chunk_length: int = 600,
        streaming: bool = False,
        seed: Optional[int] = None
    ) -> bool:
        """
        Generate speech from text using Fish Speech API
        
        Args:
            text: Text to synthesize
            output_path: Path to save generated audio
            reference_audio_paths: Optional list of reference audio file paths
            reference_text_paths: Optional list of reference text file paths
            reference_id: Optional reference ID (alternative to audio/text paths)
            language: Target language (auto-detected if not specified)
            format: Audio format (wav, mp3, flac)
            temperature: Sampling temperature
            top_p: Top-p sampling parameter
            repetition_penalty: Repetition penalty
            max_new_tokens: Maximum new tokens to generate
            chunk_length: Chunk length for synthesis
            streaming: Enable streaming response
            seed: Random seed for deterministic generation
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Prepare reference data
            reference_audios = None
            reference_texts = None
            
            if reference_audio_paths:
                reference_audios = [self._read_audio_file(path) for path in reference_audio_paths]
            
            if reference_text_paths:
                reference_texts = [self._read_text_file(path) for path in reference_text_paths]
            
            # Create request data
            request_data = self._create_tts_request(
                text=text,
                reference_audios=reference_audios,
                reference_texts=reference_texts,
                reference_id=reference_id,
                format=format,
                max_new_tokens=max_new_tokens,
                chunk_length=chunk_length,
                top_p=top_p,
                repetition_penalty=repetition_penalty,
                temperature=temperature,
                streaming=streaming,
                seed=seed
            )
            
            # Serialize request data
            serialized_data = ormsgpack.packb(request_data)
            
            # Send request
            url = f"{self.base_url}/v1/tts"
            headers = {
                "content-type": "application/msgpack"
            }
            
            self.logger.info(f"Sending TTS request to {url}")
            self.logger.info(f"Text: {text[:100]}{'...' if len(text) > 100 else ''}")
            
            start_time = time.time()
            response = self.session.post(
                url,
                data=serialized_data,
                headers=headers,
                timeout=self.timeout,
                stream=streaming
            )
            
            if response.status_code == 200:
                # Save audio content
                with open(output_path, 'wb') as f:
                    if streaming:
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                f.write(chunk)
                    else:
                        f.write(response.content)
                
                generation_time = time.time() - start_time
                self.logger.info(f"Speech generated successfully in {generation_time:.2f}s")
                self.logger.info(f"Audio saved to: {output_path}")
                return True
            else:
                self.logger.error(f"TTS request failed with status {response.status_code}")
                try:
                    error_data = ormsgpack.unpackb(response.content)
                    self.logger.error(f"Error details: {error_data}")
                except:
                    self.logger.error(f"Raw error response: {response.content}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error generating speech: {e}")
            return False

    def test_connection(self) -> bool:
        """Test connection to Fish Speech API server"""
        try:
            # Try to reach the health/status endpoint (if available)
            # Otherwise, we'll just check if the server is responding
            url = f"{self.base_url}/health"
            response = self.session.get(url, timeout=5.0)
            
            if response.status_code in [200, 404]:  # 404 is fine, means server is up
                self.logger.info("Fish Speech API server is reachable")
                return True
            else:
                self.logger.warning(f"Server responded with status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.logger.error(f"Cannot connect to Fish Speech API server at {self.base_url}")
            return False
        except Exception as e:
            self.logger.error(f"Error testing connection: {e}")
            return False

    def get_server_info(self) -> Optional[Dict[str, Any]]:
        """Get server information if available"""
        try:
            url = f"{self.base_url}/v1/info"
            response = self.session.get(url, timeout=5.0)
            
            if response.status_code == 200:
                return ormsgpack.unpackb(response.content)
            else:
                self.logger.warning("Server info endpoint not available")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting server info: {e}")
            return None


def main():
    """Test the FishSpeechClient"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Fish Speech Client")
    parser.add_argument("--text", "-t", type=str, default="Hello, this is a test of Fish Speech TTS。", help="Text to synthesize")
    parser.add_argument("--output", "-o", type=str, default="test_output.wav", help="Output audio file")
    parser.add_argument("--url", "-u", type=str, default="http://0.0.0.0:8080", help="Fish Speech API URL")
    parser.add_argument("--api-key", type=str, help="API key for authentication")
    parser.add_argument("--reference_audio", type=str, default="reference_audio.wav", help="Reference audio file path")
    parser.add_argument("--reference_text", type=str, default="reference_audio.txt", help="Reference text file path")
    parser.add_argument("--language", type=str, default="jp", help="Target language")
    
    args = parser.parse_args()
    
    # Create client
    client = FishSpeechClient(
        base_url=args.url,
        api_key=args.api_key,
        timeout=120.0
    )
    
    # Test connection
    if not client.test_connection():
        print("Failed to connect to Fish Speech API server")
        return
    
    # Get server info
    info = client.get_server_info()
    if info:
        print(f"Server info: {info}")
    
    # Prepare reference data
    ref_audio_paths = [args.reference_audio] if args.reference_audio else None
    ref_text_paths = [args.reference_text] if args.reference_text else None
    
    # Generate speech
    success = client.generate_speech(
        text=args.text,
        output_path=args.output,
        reference_audio_paths=ref_audio_paths,
        reference_text_paths=ref_text_paths,
        language=args.language,
        seed=42
    )
    
    if success:
        print(f"Speech synthesis completed successfully! Audio saved to: {args.output}")
    else:
        print("Speech synthesis failed")


if __name__ == "__main__":
    main()