<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #f5f5f5;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #2ecc71;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 30px;
      }
      .image-text-section {
        display: flex;
        gap: 30px;
      }
      .image-container {
        flex: 0 0 400px;
      }
      .image {
        width: 400px;
        height: auto;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }
      .text-wrap {
        flex: 1;
        font-size: 20px;
        line-height: 1.6;
      }
      .text-section {
        margin-bottom: 20px;
      }
      .highlight {
        color: #2ecc71;
        font-weight: bold;
      }
      .caption {
        font-size: 16px;
        color: #666;
        text-align: center;
        margin-top: 10px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">图文混排布局</div>
      <div class="content">
        <div class="image-text-section">
          <div class="image-container">
            <img class="image" src="/home/<USER>/upload/search_images/kHPRPSysnPAD.png" alt="AI语音合成技术图示">
            <div class="caption">AI语音合成技术流程图</div>
          </div>
          <div class="text-wrap">
            <div class="text-section">
              <p>图文混排布局是演示文稿中常见的设计方式，它将文字和图像结合在一起，以增强信息的表达效果。这种布局对于TTS系统来说是一个挑战，因为系统需要确定<span class="highlight">正确的阅读顺序</span>。</p>
              <p>在左侧图像的情况下，标准的阅读顺序应该是先读取图像的替代文本或标题，然后再读取右侧的文字内容。但在实际应用中，TTS系统可能会遇到困难，尤其是当图像和文字之间没有明确的视觉分隔时。</p>
            </div>
          </div>
        </div>
        
        <div class="text-section">
          <p>图文混排的挑战在于，人类读者可以通过视觉线索快速确定阅读顺序，而TTS系统则需要依靠算法来分析页面结构。在设计演示文稿时，应考虑以下因素来优化TTS的阅读体验：</p>
        </div>
        
        <div class="image-text-section" style="flex-direction: row-reverse;">
          <div class="image-container">
            <img class="image" src="/home/<USER>/upload/search_images/JIoLVU1t6nJX.png" alt="AI驱动的文本到语音转换">
            <div class="caption">AI驱动的文本到语音转换应用</div>
          </div>
          <div class="text-wrap">
            <div class="text-section">
              <p>对于右侧图像的布局，TTS系统面临着类似的挑战。理想情况下，系统应该先读取左侧的文字内容，然后再处理图像的相关信息。这要求TTS系统能够<span class="highlight">识别页面的空间结构</span>，并据此确定合理的阅读顺序。</p>
              <p>为了帮助TTS系统正确处理图文混排布局，设计者可以添加明确的阅读顺序指示，例如编号、箭头或其他视觉引导元素。此外，确保图像具有适当的替代文本也是提高可访问性的重要措施。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

