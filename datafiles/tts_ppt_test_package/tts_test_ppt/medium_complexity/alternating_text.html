<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #f5f5f5;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #2ecc71;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .intro {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 20px;
      }
      .zigzag-container {
        display: flex;
        flex-direction: column;
        gap: 30px;
      }
      .zigzag-row {
        display: flex;
        align-items: center;
        gap: 20px;
      }
      .zigzag-row.reverse {
        flex-direction: row-reverse;
      }
      .zigzag-number {
        background-color: #2ecc71;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        flex-shrink: 0;
      }
      .zigzag-content {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        flex: 1;
        position: relative;
      }
      .zigzag-content::before {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-style: solid;
      }
      .zigzag-row:not(.reverse) .zigzag-content::before {
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
        border-width: 10px 10px 10px 0;
        border-color: transparent white transparent transparent;
      }
      .zigzag-row.reverse .zigzag-content::before {
        right: -10px;
        top: 50%;
        transform: translateY(-50%);
        border-width: 10px 0 10px 10px;
        border-color: transparent transparent transparent white;
      }
      .zigzag-title {
        font-size: 22px;
        font-weight: bold;
        color: #2ecc71;
        margin-bottom: 10px;
      }
      .zigzag-text {
        font-size: 18px;
        line-height: 1.5;
      }
      .conclusion {
        font-size: 20px;
        font-style: italic;
        text-align: center;
        margin-top: 20px;
        padding: 15px;
        background: #e8f8f0;
        border-radius: 10px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">交错文本布局</div>
      <div class="content">
        <div class="intro">
          交错文本布局是一种非线性的内容排列方式，文本块以交替的方式分布在页面的左右两侧。这种布局对TTS系统的阅读顺序识别提出了更高的挑战。
        </div>
        
        <div class="zigzag-container">
          <div class="zigzag-row">
            <div class="zigzag-number">1</div>
            <div class="zigzag-content">
              <div class="zigzag-title">交错布局的挑战</div>
              <div class="zigzag-text">
                交错文本布局打破了传统的从左到右、从上到下的阅读模式，文本块在水平方向上交替出现。这种布局在视觉上很有吸引力，但对TTS系统来说是一个挑战。
              </div>
            </div>
          </div>
          
          <div class="zigzag-row reverse">
            <div class="zigzag-number">2</div>
            <div class="zigzag-content">
              <div class="zigzag-title">阅读顺序识别</div>
              <div class="zigzag-text">
                在交错布局中，TTS系统需要识别出文本块之间的逻辑关系，而不仅仅依赖于它们的空间位置。编号或其他顺序指示符可以帮助系统确定正确的阅读顺序。
              </div>
            </div>
          </div>
          
          <div class="zigzag-row">
            <div class="zigzag-number">3</div>
            <div class="zigzag-content">
              <div class="zigzag-title">视觉连续性</div>
              <div class="zigzag-text">
                尽管文本块在空间上是交错排列的，但它们在内容上应该保持连续性。这种连续性有助于TTS系统理解内容之间的关联，从而按照正确的顺序进行阅读。
              </div>
            </div>
          </div>
          
          <div class="zigzag-row reverse">
            <div class="zigzag-number">4</div>
            <div class="zigzag-content">
              <div class="zigzag-title">设计考虑</div>
              <div class="zigzag-text">
                在设计交错布局时，应考虑TTS用户的需求。明确的视觉引导、一致的样式和清晰的编号系统可以帮助TTS系统正确解析页面结构，提供更好的阅读体验。
              </div>
            </div>
          </div>
        </div>
        
        <div class="conclusion">
          通过测试交错文本布局的TTS处理效果，可以评估系统对非线性内容排列的适应能力，为优化TTS算法提供有价值的参考。
        </div>
      </div>
    </div>
  </body>
</html>

