<!DOCTYPE html>
<html lang="ja">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .bullet-list {
        margin-left: 20px;
      }
      .bullet-item {
        font-size: 24px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }
      .bullet-icon {
        color: #3498db;
        margin-right: 15px;
      }
      .paragraph {
        font-size: 20px;
        line-height: 1.5;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">標準テキストレイアウト</div>
      <div class="content">
        <div class="bullet-list">
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第一項：標準テキストレイアウトは基本的なTTSテストに適しています</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第二項：テキストは左から右、上から下に配置されます</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第三項：明確な階層構造はTTSの認識を助けます</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第四項：標準化されたテキスト形式は読み上げの精度を向上させます</span>
          </div>
        </div>
        <div class="paragraph">
          光学文字認識（OCR）技術は、通常、標準レイアウトのテキストに対して最も良い認識効果を発揮します。PPTを設計する際に、規範的なテキスト配置方法を採用することで、TTSの読み上げ精度を著しく向上させることができます。標準レイアウトは、人間が読みやすいだけでなく、AIシステムがコンテンツ構造を理解し、正しい順序で音声合成を行うのにも役立ちます。このレイアウト方法は、重要な情報を含むプレゼンテーションに特に適しており、情報が正確に伝達されることを保証します。
        </div>
      </div>
    </div>
  </body>
</html>
