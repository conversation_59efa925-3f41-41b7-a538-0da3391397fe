<!DOCTYPE html>
<html lang="ja">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        gap: 40px;
        height: 100%;
      }
      .column {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .column-title {
        font-size: 24px;
        font-weight: bold;
        color: #3498db;
        margin-bottom: 10px;
        border-bottom: 2px solid #3498db;
        padding-bottom: 5px;
      }
      .column-text {
        font-size: 20px;
        line-height: 1.5;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">2段組レイアウト</div>
      <div class="content">
        <div class="column">
          <div class="column-title">左側のコンテンツ</div>
          <div class="column-text">
            <p>2段組レイアウトは一般的なPPTのデザイン方法で、通常、コンテンツを比較または並べて表示するために使用されます。TTS読み上げテストでは、このレイアウトを使用して、システムが水平方向の読み上げ順序を正しく認識できるか検証できます。</p>
            <p>標準的な読み上げ順序は、まず左側のコラムのすべてのコンテンツを読み、次に右側のコラムのコンテンツを読むべきです。このレイアウトは、TTSシステムの空間認識能力に対してより高い要求をします。</p>
            <p>左側のコラムは通常、主要なコンテンツや前提条件を配置するために使用され、読み上げの開始位置です。設計時には、左側のコンテンツと右側のコンテンツが明確に視覚的に区別されるようにする必要があります。</p>
          </div>
        </div>
        <div class="column">
          <div class="column-title">右側のコンテンツ</div>
          <div class="column-text">
            <p>右側のコラムは通常、二次的なコンテンツ、補足説明、または結論を配置するために使用されます。TTSテストでは、システムは左側のコンテンツを読み終えた後に右側のコンテンツを読むべきです。</p>
            <p>TTSシステムが読み上げ順序を正しく認識できるように、明確な視覚的な区切り線、異なる背景色、または番号付けシステムを使用して、コンテンツの前後関係を示すことができます。</p>
            <p>複雑なプレゼンテーションでは、2段組レイアウトが他のレイアウト要素と混合して使用される場合があり、これはTTSシステムの認識能力にとってより大きな課題となります。テスト時には、システムがこの複雑な状況を正しく処理できるかどうかに注意を払う必要があります。</p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
