<!DOCTYPE html>
<html lang="ja">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .intro-text {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 20px;
      }
      .numbered-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .list-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
      }
      .number {
        background-color: #3498db;
        color: white;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        flex-shrink: 0;
      }
      .item-content {
        font-size: 20px;
        line-height: 1.5;
        padding-top: 5px;
      }
      .conclusion {
        font-size: 20px;
        line-height: 1.5;
        margin-top: 20px;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">番号付きリスト</div>
      <div class="content">
        <div class="intro-text">
          番号付きリストは、プレゼンテーションで構造化されたコンテンツを表示するためによく使用される方法であり、TTSシステムに明確な読み上げ順序のガイダンスを提供します。以下は、TTSが番号付きリストを処理する際の主な考慮事項です。
        </div>
        <div class="numbered-list">
          <div class="list-item">
            <div class="number">1</div>
            <div class="item-content">
              <strong>順序認識</strong>：TTSシステムは、リスト項目が視覚的にどのように配置されていても、番号を認識し、数字の順序で読み上げることができる必要があります。
            </div>
          </div>
          <div class="list-item">
            <div class="number">2</div>
            <div class="item-content">
              <strong>階層構造</strong>：多階層リストの場合、TTSはメインリストとサブリストの階層関係を正しく認識し、適切な順序で読み上げる必要があります。
            </div>
          </div>
          <div class="list-item">
            <div class="number">3</div>
            <div class="item-content">
              <strong>音声表現</strong>：TTSシステムは、単に数字を読み上げるだけでなく、「第一点」、「第二点」のように番号を適切に表現できる必要があります。
            </div>
          </div>
          <div class="list-item">
            <div class="number">4</div>
            <div class="item-content">
              <strong>一時停止の制御</strong>：リスト項目間には適切な一時停止を設け、聞き手が異なるリスト項目を明確に区別できるようにする必要があります。
            </div>
          </div>
          <div class="list-item">
            <div class="number">5</div>
            <div class="item-content">
              <strong>視覚的一貫性</strong>：番号付きリストの視覚的デザインは一貫性を保ち、TTSシステムがリスト構造を正しく認識できるようにする必要があります。
            </div>
          </div>
        </div>
        <div class="conclusion">
          番号付きリストのTTS処理効果をテストすることで、システムの構造化コンテンツに対する理解能力と読み上げ順序の正確性を評価できます。
        </div>
      </div>
    </div>
  </body>
</html>
