<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #34495e;
        font-family: Arial, sans-serif;
        color: #ffffff;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #9b59b6;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .intro {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 20px;
      }
      .content {
        position: relative;
        height: 500px;
      }
      .node {
        position: absolute;
        background: rgba(155, 89, 182, 0.8);
        border-radius: 10px;
        padding: 15px;
        width: 200px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
      }
      .node-title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .node-number {
        background: white;
        color: #9b59b6;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-right: 10px;
      }
      .node-name {
        font-size: 20px;
        font-weight: bold;
      }
      .node-content {
        font-size: 16px;
        line-height: 1.4;
      }
      .node-1 {
        top: 50px;
        left: 150px;
      }
      .node-2 {
        top: 180px;
        left: 400px;
      }
      .node-3 {
        top: 50px;
        right: 150px;
      }
      .node-4 {
        top: 300px;
        left: 150px;
      }
      .node-5 {
        top: 300px;
        right: 150px;
      }
      .node-6 {
        bottom: 50px;
        left: 400px;
      }
      .path {
        position: absolute;
        border: 2px dashed rgba(255, 255, 255, 0.5);
        z-index: -1;
      }
      .path-1-2 {
        width: 100px;
        top: 100px;
        left: 350px;
        transform: rotate(45deg);
      }
      .path-2-3 {
        width: 200px;
        top: 180px;
        left: 600px;
      }
      .path-3-5 {
        height: 200px;
        top: 150px;
        right: 250px;
      }
      .path-2-4 {
        height: 100px;
        top: 230px;
        left: 450px;
      }
      .path-4-6 {
        width: 100px;
        top: 380px;
        left: 350px;
        transform: rotate(45deg);
      }
      .path-5-6 {
        width: 100px;
        top: 380px;
        left: 600px;
        transform: rotate(-45deg);
      }
      .arrow {
        position: absolute;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
      }
      .arrow-1-2 {
        top: 140px;
        left: 370px;
        transform: rotate(45deg);
      }
      .arrow-2-3 {
        top: 170px;
        left: 680px;
      }
      .arrow-3-5 {
        top: 240px;
        right: 240px;
      }
      .arrow-2-4 {
        top: 270px;
        left: 440px;
      }
      .arrow-4-6 {
        top: 420px;
        left: 370px;
        transform: rotate(45deg);
      }
      .arrow-5-6 {
        top: 420px;
        left: 600px;
        transform: rotate(-45deg);
      }
      .instruction {
        text-align: center;
        font-size: 20px;
        font-style: italic;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">非线性阅读路径</div>
      <div class="intro">
        非线性阅读路径打破了传统的从左到右、从上到下的阅读模式，内容以网络或图形的方式连接，阅读顺序可能跳跃或分支。这种布局对TTS系统提出了极大的挑战，因为系统需要确定复杂的阅读路径。
      </div>
      <div class="content">
        <!-- 连接路径 -->
        <div class="path path-1-2"></div>
        <div class="path path-2-3"></div>
        <div class="path path-3-5"></div>
        <div class="path path-2-4"></div>
        <div class="path path-4-6"></div>
        <div class="path path-5-6"></div>
        
        <!-- 箭头指示 -->
        <div class="arrow arrow-1-2"><i class="fas fa-arrow-right"></i></div>
        <div class="arrow arrow-2-3"><i class="fas fa-arrow-right"></i></div>
        <div class="arrow arrow-3-5"><i class="fas fa-arrow-down"></i></div>
        <div class="arrow arrow-2-4"><i class="fas fa-arrow-down"></i></div>
        <div class="arrow arrow-4-6"><i class="fas fa-arrow-right"></i></div>
        <div class="arrow arrow-5-6"><i class="fas fa-arrow-left"></i></div>
        
        <!-- 内容节点 -->
        <div class="node node-1">
          <div class="node-title">
            <div class="node-number">1</div>
            <div class="node-name">起点</div>
          </div>
          <div class="node-content">
            非线性阅读路径通常有一个明确的起点，但之后的阅读顺序可能分支或跳跃。TTS系统需要识别这个起点，并从这里开始阅读。
          </div>
        </div>
        
        <div class="node node-2">
          <div class="node-title">
            <div class="node-number">2</div>
            <div class="node-name">分支点</div>
          </div>
          <div class="node-content">
            分支点是阅读路径分叉的位置，TTS系统需要决定接下来应该读取哪个分支。这通常需要基于上下文或明确的指示来做出决策。
          </div>
        </div>
        
        <div class="node node-3">
          <div class="node-title">
            <div class="node-number">3</div>
            <div class="node-name">路径A</div>
          </div>
          <div class="node-content">
            这是从分支点延伸出的一条路径。TTS系统需要能够跟踪这条路径，并在适当的时候返回到主路径或继续到下一个节点。
          </div>
        </div>
        
        <div class="node node-4">
          <div class="node-title">
            <div class="node-number">4</div>
            <div class="node-name">路径B</div>
          </div>
          <div class="node-content">
            这是另一条从分支点延伸出的路径。TTS系统需要确定是先读取路径A还是路径B，或者是否应该按照某种特定的顺序读取。
          </div>
        </div>
        
        <div class="node node-5">
          <div class="node-title">
            <div class="node-number">5</div>
            <div class="node-name">汇合点</div>
          </div>
          <div class="node-content">
            汇合点是多条路径会合的位置。TTS系统需要确保在读取汇合点之前，已经读取了所有相关的前置内容。
          </div>
        </div>
        
        <div class="node node-6">
          <div class="node-title">
            <div class="node-number">6</div>
            <div class="node-name">终点</div>
          </div>
          <div class="node-content">
            非线性路径通常有一个明确的终点。TTS系统需要确保在到达终点之前，已经读取了所有必要的内容，不遗漏任何重要信息。
          </div>
        </div>
      </div>
      <div class="instruction">
        按照箭头指示的路径阅读内容
      </div>
    </div>
  </body>
</html>

