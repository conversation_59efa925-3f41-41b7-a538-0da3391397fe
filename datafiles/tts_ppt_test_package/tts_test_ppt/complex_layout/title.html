<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        font-family: Arial, sans-serif;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
        position: relative;
        overflow: hidden;
      }
      .title {
        font-size: 72px;
        font-weight: bold;
        margin-bottom: 30px;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        z-index: 2;
      }
      .subtitle {
        font-size: 36px;
        margin-bottom: 60px;
        text-align: center;
        z-index: 2;
      }
      .footer {
        font-size: 24px;
        position: relative;
        bottom: -100px;
        z-index: 2;
      }
      .background-text {
        position: absolute;
        font-size: 180px;
        opacity: 0.1;
        font-weight: bold;
        transform: rotate(-30deg);
        white-space: nowrap;
        z-index: 1;
      }
      .bg-text-1 {
        top: -50px;
        left: -100px;
      }
      .bg-text-2 {
        bottom: -50px;
        right: -100px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="background-text bg-text-1">TTS TEST</div>
      <div class="background-text bg-text-2">COMPLEX</div>
      <div class="title">TTS测试 - 复杂布局</div>
      <div class="subtitle">文字位置处理测试用PPT</div>
      <div class="footer">测试极具挑战性的文字排版场景</div>
    </div>
  </body>
</html>

