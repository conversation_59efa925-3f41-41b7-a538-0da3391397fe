<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #34495e;
        font-family: Arial, sans-serif;
        color: #ffffff;
        padding: 40px;
        display: flex;
        flex-direction: column;
        position: relative;
      }
      .header {
        background-color: #9b59b6;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
        z-index: 10;
      }
      .content {
        position: relative;
        height: 550px;
      }
      .text-block {
        position: absolute;
        background: rgba(155, 89, 182, 0.2);
        border: 2px solid #9b59b6;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
      }
      .text-block-1 {
        top: 20px;
        left: 50px;
        width: 300px;
        transform: rotate(-5deg);
        z-index: 5;
      }
      .text-block-2 {
        top: 100px;
        left: 400px;
        width: 350px;
        transform: rotate(3deg);
        z-index: 4;
      }
      .text-block-3 {
        top: 250px;
        left: 200px;
        width: 320px;
        transform: rotate(-8deg);
        z-index: 6;
      }
      .text-block-4 {
        top: 180px;
        right: 80px;
        width: 280px;
        transform: rotate(5deg);
        z-index: 3;
      }
      .text-block-5 {
        bottom: 50px;
        right: 150px;
        width: 330px;
        transform: rotate(-3deg);
        z-index: 2;
      }
      .text-block-6 {
        bottom: 100px;
        left: 100px;
        width: 270px;
        transform: rotate(6deg);
        z-index: 1;
      }
      .block-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #9b59b6;
      }
      .block-content {
        font-size: 18px;
        line-height: 1.4;
      }
      .block-number {
        position: absolute;
        top: -15px;
        left: -15px;
        background: #9b59b6;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 18px;
      }
      .instruction {
        position: absolute;
        bottom: 20px;
        left: 0;
        width: 100%;
        text-align: center;
        font-size: 20px;
        font-style: italic;
        color: #ffffff;
        z-index: 10;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">混沌文字布局</div>
      <div class="content">
        <div class="text-block text-block-1">
          <div class="block-number">1</div>
          <div class="block-title">混沌布局挑战</div>
          <div class="block-content">
            混沌文字布局打破了传统的排版规则，文本块以不规则的方式分布在页面上，并且可能有不同的旋转角度。这种布局对TTS系统提出了极大的挑战。
          </div>
        </div>
        
        <div class="text-block text-block-2">
          <div class="block-number">2</div>
          <div class="block-title">空间识别</div>
          <div class="block-content">
            在混沌布局中，TTS系统需要具备强大的空间识别能力，能够分析文本块的位置关系，并据此确定合理的阅读顺序。编号系统在这种情况下尤为重要。
          </div>
        </div>
        
        <div class="text-block text-block-3">
          <div class="block-number">3</div>
          <div class="block-title">旋转文本处理</div>
          <div class="block-content">
            旋转的文本对OCR和TTS系统都是一个挑战。系统需要先识别文本的方向，然后进行正确的旋转处理，最后再进行文本识别和语音合成。
          </div>
        </div>
        
        <div class="text-block text-block-4">
          <div class="block-number">4</div>
          <div class="block-title">视觉引导</div>
          <div class="block-content">
            在混沌布局中，视觉引导元素如编号、箭头或连接线可以帮助TTS系统确定正确的阅读顺序，提高阅读的准确性和流畅性。
          </div>
        </div>
        
        <div class="text-block text-block-5">
          <div class="block-number">5</div>
          <div class="block-title">用户体验考虑</div>
          <div class="block-content">
            尽管混沌布局在视觉上可能很有吸引力，但设计者应考虑TTS用户的体验。过于复杂的布局可能会导致阅读混乱，影响信息传达。
          </div>
        </div>
        
        <div class="text-block text-block-6">
          <div class="block-number">6</div>
          <div class="block-title">测试价值</div>
          <div class="block-content">
            测试混沌文字布局的TTS处理效果，可以评估系统在极端情况下的表现，为算法优化提供有价值的参考数据。
          </div>
        </div>
      </div>
      <div class="instruction">按照编号顺序阅读文本块</div>
    </div>
  </body>
</html>

