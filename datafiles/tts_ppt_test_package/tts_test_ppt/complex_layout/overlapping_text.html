<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #34495e;
        font-family: Arial, sans-serif;
        color: #ffffff;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #9b59b6;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .intro {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 30px;
      }
      .content {
        position: relative;
        height: 450px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .text-layer {
        position: absolute;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        transition: transform 0.3s ease;
      }
      .text-layer:hover {
        transform: scale(1.05);
        z-index: 10 !important;
      }
      .layer-1 {
        background: rgba(155, 89, 182, 0.9);
        width: 500px;
        height: 300px;
        top: 50px;
        left: 100px;
        z-index: 5;
      }
      .layer-2 {
        background: rgba(52, 152, 219, 0.9);
        width: 450px;
        height: 280px;
        top: 100px;
        left: 250px;
        z-index: 4;
      }
      .layer-3 {
        background: rgba(46, 204, 113, 0.9);
        width: 480px;
        height: 260px;
        top: 150px;
        left: 400px;
        z-index: 3;
      }
      .layer-4 {
        background: rgba(230, 126, 34, 0.9);
        width: 420px;
        height: 240px;
        top: 200px;
        left: 550px;
        z-index: 2;
      }
      .layer-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }
      .layer-number {
        background: white;
        color: #34495e;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-right: 10px;
      }
      .layer-content {
        font-size: 18px;
        line-height: 1.4;
      }
      .instruction {
        text-align: center;
        font-size: 20px;
        font-style: italic;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">重叠文本布局</div>
      <div class="intro">
        重叠文本布局是一种高度复杂的设计方式，多个文本层相互重叠，形成视觉上的深度效果。这种布局对TTS系统提出了极大的挑战，因为系统需要确定哪些文本应该被读取，以及它们的正确阅读顺序。
      </div>
      <div class="content">
        <div class="text-layer layer-1">
          <div class="layer-title">
            <div class="layer-number">1</div>
            <span>重叠识别挑战</span>
          </div>
          <div class="layer-content">
            在重叠文本布局中，TTS系统面临的首要挑战是识别出哪些文本是可见的、可读的。当文本相互重叠时，系统需要确定哪些内容应该被忽略，哪些内容应该被读取。这通常需要复杂的图像分析和文本分割算法。
          </div>
        </div>
        
        <div class="text-layer layer-2">
          <div class="layer-title">
            <div class="layer-number">2</div>
            <span>层次顺序处理</span>
          </div>
          <div class="layer-content">
            重叠文本通常具有明确的层次关系，位于上层的文本应该优先被读取。TTS系统需要识别这种层次关系，并据此确定正确的阅读顺序。这要求系统具备对页面深度结构的理解能力。
          </div>
        </div>
        
        <div class="text-layer layer-3">
          <div class="layer-title">
            <div class="layer-number">3</div>
            <span>透明度与可见性</span>
          </div>
          <div class="layer-content">
            半透明文本层增加了识别难度，因为下层文本可能部分可见。TTS系统需要判断文本的可见程度，并决定是否应该读取这些部分可见的内容。这通常需要结合透明度分析和文本识别技术。
          </div>
        </div>
        
        <div class="text-layer layer-4">
          <div class="layer-title">
            <div class="layer-number">4</div>
            <span>设计建议</span>
          </div>
          <div class="layer-content">
            尽管重叠文本在视觉上很有吸引力，但从可访问性角度考虑，应谨慎使用。如果必须使用重叠布局，建议提供明确的阅读顺序指示，并确保重要信息不会因重叠而丢失。
          </div>
        </div>
      </div>
      <div class="instruction">
        悬停在文本层上可以突出显示该层（鼠标交互）
      </div>
    </div>
  </body>
</html>

