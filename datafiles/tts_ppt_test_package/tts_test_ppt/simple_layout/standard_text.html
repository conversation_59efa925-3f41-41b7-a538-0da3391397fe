<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .bullet-list {
        margin-left: 20px;
      }
      .bullet-item {
        font-size: 24px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }
      .bullet-icon {
        color: #3498db;
        margin-right: 15px;
      }
      .paragraph {
        font-size: 20px;
        line-height: 1.5;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">标准文本布局</div>
      <div class="content">
        <div class="bullet-list">
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第一项：标准文本布局适合基础TTS测试</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第二项：文字从左到右、从上到下排列</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第三项：清晰的层次结构有助于TTS识别</span>
          </div>
          <div class="bullet-item">
            <i class="fas fa-circle bullet-icon"></i>
            <span>第四项：标准化的文本格式提高阅读准确率</span>
          </div>
        </div>
        <div class="paragraph">
          文字识别技术（OCR）通常对标准布局的文本识别效果最好。在设计PPT时，采用规范的文字排列方式可以显著提高TTS的阅读准确性。标准布局不仅便于人类阅读，也有利于AI系统理解内容结构，从而按照正确的顺序进行语音合成。这种布局方式特别适合包含重要信息的演示文稿，确保信息能被准确传达。
        </div>
      </div>
    </div>
  </body>
</html>

