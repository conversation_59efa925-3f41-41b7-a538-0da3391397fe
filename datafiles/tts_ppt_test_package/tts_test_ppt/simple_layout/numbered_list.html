<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .intro-text {
        font-size: 20px;
        line-height: 1.5;
        margin-bottom: 20px;
      }
      .numbered-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .list-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
      }
      .number {
        background-color: #3498db;
        color: white;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        flex-shrink: 0;
      }
      .item-content {
        font-size: 20px;
        line-height: 1.5;
        padding-top: 5px;
      }
      .conclusion {
        font-size: 20px;
        line-height: 1.5;
        margin-top: 20px;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">编号列表</div>
      <div class="content">
        <div class="intro-text">
          编号列表是演示文稿中常用的结构化内容展示方式，它为TTS系统提供了明确的阅读顺序指引。以下是TTS处理编号列表的关键考虑因素：
        </div>
        <div class="numbered-list">
          <div class="list-item">
            <div class="number">1</div>
            <div class="item-content">
              <strong>顺序识别</strong>：TTS系统应能识别编号并按照数字顺序进行阅读，无论列表项在视觉上如何排列。
            </div>
          </div>
          <div class="list-item">
            <div class="number">2</div>
            <div class="item-content">
              <strong>层次结构</strong>：对于多级列表，TTS应能正确识别主列表和子列表的层次关系，并按照适当的顺序进行阅读。
            </div>
          </div>
          <div class="list-item">
            <div class="number">3</div>
            <div class="item-content">
              <strong>语音表达</strong>：TTS系统应能适当地表达编号，例如"第一点"、"第二点"，而不仅仅是读出数字。
            </div>
          </div>
          <div class="list-item">
            <div class="number">4</div>
            <div class="item-content">
              <strong>停顿控制</strong>：在列表项之间应有适当的停顿，以便听众能够清晰地区分不同的列表项。
            </div>
          </div>
          <div class="list-item">
            <div class="number">5</div>
            <div class="item-content">
              <strong>视觉一致性</strong>：编号列表的视觉设计应保持一致，以帮助TTS系统正确识别列表结构。
            </div>
          </div>
        </div>
        <div class="conclusion">
          通过测试编号列表的TTS处理效果，可以评估系统对结构化内容的理解能力和阅读顺序的准确性。
        </div>
      </div>
    </div>
  </body>
</html>

