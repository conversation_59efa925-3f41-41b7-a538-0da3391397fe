<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        font-family: Arial, sans-serif;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
      }
      .title {
        font-size: 72px;
        font-weight: bold;
        margin-bottom: 30px;
        text-align: center;
      }
      .subtitle {
        font-size: 36px;
        margin-bottom: 60px;
        text-align: center;
      }
      .footer {
        font-size: 24px;
        position: relative;
        bottom: -100px;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="title">TTS测试 - 简单布局</div>
      <div class="subtitle">文字位置处理测试用PPT</div>
      <div class="footer">适用于AI TTS阅读顺序测试</div>
    </div>
  </body>
</html>

