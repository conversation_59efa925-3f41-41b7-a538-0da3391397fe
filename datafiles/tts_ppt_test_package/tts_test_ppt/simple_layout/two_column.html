<!DOCTYPE html>
<html lang="zh">
  <head>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .slide-container {
        width: 1280px;
        min-height: 720px;
        background: #ffffff;
        font-family: Arial, sans-serif;
        color: #333333;
        padding: 40px;
        display: flex;
        flex-direction: column;
      }
      .header {
        background-color: #3498db;
        color: white;
        padding: 15px 30px;
        font-size: 36px;
        font-weight: bold;
        border-radius: 5px;
        margin-bottom: 30px;
      }
      .content {
        display: flex;
        gap: 40px;
        height: 100%;
      }
      .column {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .column-title {
        font-size: 24px;
        font-weight: bold;
        color: #3498db;
        margin-bottom: 10px;
        border-bottom: 2px solid #3498db;
        padding-bottom: 5px;
      }
      .column-text {
        font-size: 20px;
        line-height: 1.5;
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="header">两栏布局</div>
      <div class="content">
        <div class="column">
          <div class="column-title">左侧内容</div>
          <div class="column-text">
            <p>两栏布局是常见的PPT设计方式，通常用于对比或并列展示内容。在TTS阅读测试中，这种布局可以验证系统是否能正确识别水平方向的阅读顺序。</p>
            <p>标准的阅读顺序应该是先读完左侧栏的全部内容，再读取右侧栏的内容。这种布局对TTS系统的空间识别能力提出了更高要求。</p>
            <p>左侧栏通常用于放置主要内容或前提条件，是阅读的起始位置。在设计时，应确保左侧内容与右侧内容有明确的视觉区分。</p>
          </div>
        </div>
        <div class="column">
          <div class="column-title">右侧内容</div>
          <div class="column-text">
            <p>右侧栏通常用于放置次要内容、补充说明或结论。在TTS测试中，系统应该在读完左侧内容后再读取右侧内容。</p>
            <p>为了帮助TTS系统正确识别阅读顺序，可以使用明确的视觉分隔线、不同的背景色或编号系统来指示内容的先后关系。</p>
            <p>在复杂的演示文稿中，两栏布局可能会与其他布局元素混合使用，这对TTS系统的识别能力是更大的挑战。测试时应关注系统是否能正确处理这种复杂情况。</p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

