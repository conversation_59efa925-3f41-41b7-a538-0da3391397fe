<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS测试PPT文件索引</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #3498db, #2ecc71, #9b59b6);
            color: white;
            padding: 30px;
            border-radius: 10px;
        }
        .ppt-section {
            background: white;
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .ppt-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .ppt-description {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        }
        .slide-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .slide-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .simple .slide-item {
            border-left-color: #3498db;
        }
        .medium .slide-item {
            border-left-color: #2ecc71;
        }
        .complex .slide-item {
            border-left-color: #9b59b6;
        }
        .slide-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .slide-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }
        .slide-link:hover {
            text-decoration: underline;
        }
        .instructions {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #2c3e50;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TTS测试PPT文件索引</h1>
        <p>专为TTS文字位置处理测试设计的演示文稿</p>
    </div>

    <div class="ppt-section simple">
        <div class="ppt-title">1. 简单布局PPT</div>
        <div class="ppt-description">
            适合基础测试，包含标准的文字排列方式，遵循从左到右、从上到下的阅读顺序。
        </div>
        <div class="slide-list">
            <div class="slide-item">
                <div class="slide-name">标题页</div>
                <a href="tts_test_ppt/simple_layout/title.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">标准文本布局</div>
                <a href="tts_test_ppt/simple_layout/standard_text.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">两栏布局</div>
                <a href="tts_test_ppt/simple_layout/two_column.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">编号列表</div>
                <a href="tts_test_ppt/simple_layout/numbered_list.html" class="slide-link">打开页面</a>
            </div>
        </div>
    </div>

    <div class="ppt-section medium">
        <div class="ppt-title">2. 中等复杂度PPT</div>
        <div class="ppt-description">
            包含图文混排和多区域布局，测试TTS系统对非线性阅读顺序的处理能力。
        </div>
        <div class="slide-list">
            <div class="slide-item">
                <div class="slide-name">标题页</div>
                <a href="tts_test_ppt/medium_complexity/title.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">图文混排布局</div>
                <a href="tts_test_ppt/medium_complexity/image_text_mix.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">多区域布局</div>
                <a href="tts_test_ppt/medium_complexity/multi_region.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">交错文本布局</div>
                <a href="tts_test_ppt/medium_complexity/alternating_text.html" class="slide-link">打开页面</a>
            </div>
        </div>
    </div>

    <div class="ppt-section complex">
        <div class="ppt-title">3. 复杂布局PPT</div>
        <div class="ppt-description">
            包含混沌文字布局、重叠文本和非线性阅读路径，测试TTS系统在极具挑战性场景下的处理能力。
        </div>
        <div class="slide-list">
            <div class="slide-item">
                <div class="slide-name">标题页</div>
                <a href="tts_test_ppt/complex_layout/title.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">混沌文字布局</div>
                <a href="tts_test_ppt/complex_layout/chaotic_text.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">重叠文本布局</div>
                <a href="tts_test_ppt/complex_layout/overlapping_text.html" class="slide-link">打开页面</a>
            </div>
            <div class="slide-item">
                <div class="slide-name">非线性阅读路径</div>
                <a href="tts_test_ppt/complex_layout/nonlinear_path.html" class="slide-link">打开页面</a>
            </div>
        </div>
    </div>

    <div class="instructions">
        <h3>使用说明</h3>
        <ul>
            <li><strong>浏览器要求：</strong>支持HTML5和CSS3的现代浏览器</li>
            <li><strong>分辨率：</strong>所有页面设计为1280x720像素</li>
            <li><strong>测试建议：</strong>按照复杂度从低到高的顺序进行测试</li>
            <li><strong>截图测试：</strong>可以对页面进行截图，然后使用OCR工具进行文字识别测试</li>
            <li><strong>详细说明：</strong>请参考ppt_test_guide.pdf文档获取完整的测试指南</li>
        </ul>
    </div>
</body>
</html>

