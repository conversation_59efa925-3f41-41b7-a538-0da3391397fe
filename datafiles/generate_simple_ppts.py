#!/usr/bin/env python3
"""
简化的TTS测试PPT文件生成器
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def create_simple_layout_ppt():
    """创建简单布局PPT"""
    prs = Presentation()
    prs.slide_width = Inches(16)
    prs.slide_height = Inches(9)
    
    # 1. 标题页
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "TTS测试 - 简单布局"
    subtitle.text = "文字位置处理测试用PPT\\n适用于AI TTS阅读顺序测试"
    
    # 2. 标准文本布局页
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "标准文本布局"
    content.text = "• 第一项：标准文本布局适合基础TTS测试\\n• 第二项：文字从左到右、从上到下排列\\n• 第三项：清晰的层次结构有助于TTS识别\\n• 第四项：标准化的文本格式提高阅读准确率\\n\\n文字识别技术（OCR）通常对标准布局的文本识别效果最好。在设计PPT时，采用规范的文字排列方式可以显著提高TTS的阅读准确性。"
    
    # 3. 两栏布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "两栏布局"
    
    # 左栏
    left_box = slide.shapes.add_textbox(Inches(0.5), Inches(2), Inches(7), Inches(6))
    left_frame = left_box.text_frame
    left_frame.text = "左侧内容\\n\\n两栏布局是常见的PPT设计方式，通常用于对比或并列展示内容。在TTS阅读测试中，这种布局可以验证系统是否能正确识别水平方向的阅读顺序。\\n\\n标准的阅读顺序应该是先读完左侧栏的全部内容，再读取右侧栏的内容。"
    
    # 右栏
    right_box = slide.shapes.add_textbox(Inches(8.5), Inches(2), Inches(7), Inches(6))
    right_frame = right_box.text_frame
    right_frame.text = "右侧内容\\n\\n右侧栏通常用于放置次要内容、补充说明或结论。在TTS测试中，系统应该在读完左侧内容后再读取右侧内容。\\n\\n为了帮助TTS系统正确识别阅读顺序，可以使用明确的视觉分隔线、不同的背景色或编号系统。"
    
    # 4. 编号列表页
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "编号列表"
    content.text = "编号列表是演示文稿中常用的结构化内容展示方式：\\n\\n1. 顺序识别：TTS系统应能识别编号并按照数字顺序进行阅读\\n2. 层次结构：对于多级列表，TTS应能正确识别主列表和子列表的层次关系\\n3. 语音表达：TTS系统应能适当地表达编号，例如'第一点'、'第二点'\\n4. 停顿控制：在列表项之间应有适当的停顿，以便听众能够清晰地区分\\n5. 视觉一致性：编号列表的视觉设计应保持一致，以帮助TTS系统正确识别"
    
    # 保存文件
    output_path = "/home/<USER>/tts_ppt_files/TTS测试_简单布局.pptx"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    prs.save(output_path)
    print(f"简单布局PPT已保存: {output_path}")
    return output_path

def create_medium_complexity_ppt():
    """创建中等复杂度PPT"""
    prs = Presentation()
    prs.slide_width = Inches(16)
    prs.slide_height = Inches(9)
    
    # 1. 标题页
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "TTS测试 - 中等复杂度"
    subtitle.text = "文字位置处理测试用PPT\\n测试非线性阅读顺序和图文混排"
    
    # 2. 图文混排布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "图文混排布局"
    
    # 左侧图片占位符
    shape1 = slide.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(1), Inches(2), Inches(4), Inches(3))
    shape1.fill.solid()
    shape1.fill.fore_color.rgb = RGBColor(52, 152, 219)
    shape1_text = shape1.text_frame
    shape1_text.text = "AI语音合成\\n技术图示\\n(图片位置)"
    
    # 右侧文字
    text_box1 = slide.shapes.add_textbox(Inches(5.5), Inches(2), Inches(9.5), Inches(3))
    text_frame1 = text_box1.text_frame
    text_frame1.text = "图文混排布局是演示文稿中常见的设计方式，它将文字和图像结合在一起，以增强信息的表达效果。这种布局对于TTS系统来说是一个挑战，因为系统需要确定正确的阅读顺序。\\n\\n在左侧图像的情况下，标准的阅读顺序应该是先读取图像的替代文本或标题，然后再读取右侧的文字内容。"
    
    # 下方文字
    text_box2 = slide.shapes.add_textbox(Inches(1), Inches(5.5), Inches(6), Inches(2.5))
    text_frame2 = text_box2.text_frame
    text_frame2.text = "对于右侧图像的布局，TTS系统面临着类似的挑战。理想情况下，系统应该先读取左侧的文字内容，然后再处理图像的相关信息。"
    
    # 右下角图片占位符
    shape2 = slide.shapes.add_shape(MSO_SHAPE.RECTANGLE, Inches(8), Inches(5.5), Inches(4), Inches(2.5))
    shape2.fill.solid()
    shape2.fill.fore_color.rgb = RGBColor(155, 89, 182)
    shape2_text = shape2.text_frame
    shape2_text.text = "AI驱动的\\n文本到语音转换\\n(图片位置)"
    
    # 3. 多区域布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "多区域布局"
    
    # 创建6个区域
    regions = [
        {"pos": (1, 2), "size": (4.5, 2), "text": "1. 左上区域\\n\\n多区域布局将页面分割为多个独立的内容区域，每个区域包含不同的信息。"},
        {"pos": (6, 2), "size": (4.5, 2), "text": "2. 中上区域\\n\\n在多区域布局中，传统的阅读顺序可能不再适用。TTS系统需要分析页面结构。"},
        {"pos": (11, 2), "size": (4.5, 2), "text": "3. 右上区域\\n\\n区域编号可以帮助TTS系统确定阅读顺序。明确的数字标记指示每个区域的顺序。"},
        {"pos": (1, 4.5), "size": (4.5, 2), "text": "4. 左下区域\\n\\n区域之间的视觉分隔对TTS系统很重要。明确的边界可以帮助系统识别不同区域。"},
        {"pos": (6, 4.5), "size": (4.5, 2), "text": "5. 中下区域\\n\\n在设计多区域布局时，应考虑TTS用户的体验。过于复杂的布局可能导致混乱。"},
        {"pos": (11, 4.5), "size": (4.5, 2), "text": "6. 右下区域\\n\\n测试多区域布局的TTS处理效果，可以评估系统对复杂页面结构的理解能力。"}
    ]
    
    for region in regions:
        text_box = slide.shapes.add_textbox(Inches(region["pos"][0]), Inches(region["pos"][1]), 
                                          Inches(region["size"][0]), Inches(region["size"][1]))
        text_frame = text_box.text_frame
        text_frame.text = region["text"]
    
    # 4. 交错文本布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "交错文本布局"
    
    # 介绍文字
    intro_box = slide.shapes.add_textbox(Inches(1), Inches(1.5), Inches(14), Inches(1))
    intro_frame = intro_box.text_frame
    intro_frame.text = "交错文本布局是一种非线性的内容排列方式，文本块以交替的方式分布在页面的左右两侧。"
    
    # 交错文本块
    zigzag_items = [
        {"pos": (1, 3), "text": "1. 交错布局的挑战\\n\\n交错文本布局打破了传统的阅读模式，文本块在水平方向上交替出现。"},
        {"pos": (8.5, 4), "text": "2. 阅读顺序识别\\n\\nTTS系统需要识别出文本块之间的逻辑关系，而不仅仅依赖于空间位置。"},
        {"pos": (1, 5), "text": "3. 视觉连续性\\n\\n尽管文本块在空间上是交错排列的，但它们在内容上应该保持连续性。"},
        {"pos": (8.5, 6), "text": "4. 设计考虑\\n\\n在设计交错布局时，应考虑TTS用户的需求，使用明确的视觉引导。"}
    ]
    
    for item in zigzag_items:
        text_box = slide.shapes.add_textbox(Inches(item["pos"][0]), Inches(item["pos"][1]), 
                                          Inches(6.5), Inches(1.5))
        text_frame = text_box.text_frame
        text_frame.text = item["text"]
    
    # 保存文件
    output_path = "/home/<USER>/tts_ppt_files/TTS测试_中等复杂度.pptx"
    prs.save(output_path)
    print(f"中等复杂度PPT已保存: {output_path}")
    return output_path

def create_complex_layout_ppt():
    """创建复杂布局PPT"""
    prs = Presentation()
    prs.slide_width = Inches(16)
    prs.slide_height = Inches(9)
    
    # 1. 标题页
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "TTS测试 - 复杂布局"
    subtitle.text = "文字位置处理测试用PPT\\n测试极具挑战性的文字排版场景"
    
    # 2. 混沌文字布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "混沌文字布局"
    
    # 创建混沌分布的文本块
    chaotic_blocks = [
        {"pos": (1, 2), "size": (3.5, 2), "text": "1. 混沌布局挑战\\n\\n混沌文字布局打破了传统的排版规则，文本块以不规则的方式分布在页面上。"},
        {"pos": (5.5, 2.5), "size": (4, 1.8), "text": "2. 空间识别\\n\\nTTS系统需要具备强大的空间识别能力，分析文本块的位置关系。"},
        {"pos": (10.5, 1.8), "size": (3.8, 2.2), "text": "3. 旋转文本处理\\n\\n旋转的文本对OCR和TTS系统都是一个挑战，需要先识别方向。"},
        {"pos": (2, 4.5), "size": (3.2, 1.8), "text": "4. 视觉引导\\n\\n编号、箭头或连接线可以帮助TTS系统确定正确的阅读顺序。"},
        {"pos": (6.5, 5), "size": (3.8, 2), "text": "5. 用户体验考虑\\n\\n过于复杂的布局可能会导致阅读混乱，影响信息传达。"},
        {"pos": (11, 5.5), "size": (3.5, 1.8), "text": "6. 测试价值\\n\\n测试混沌布局可以评估系统在极端情况下的表现。"}
    ]
    
    for block in chaotic_blocks:
        text_box = slide.shapes.add_textbox(Inches(block["pos"][0]), Inches(block["pos"][1]), 
                                          Inches(block["size"][0]), Inches(block["size"][1]))
        text_frame = text_box.text_frame
        text_frame.text = block["text"]
    
    # 3. 重叠文本布局页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "重叠文本布局"
    
    # 介绍文字
    intro_box = slide.shapes.add_textbox(Inches(1), Inches(1.5), Inches(14), Inches(1))
    intro_frame = intro_box.text_frame
    intro_frame.text = "重叠文本布局是一种高度复杂的设计方式，多个文本层相互重叠，形成视觉上的深度效果。"
    
    # 创建重叠的文本层
    layers = [
        {"pos": (2, 3), "size": (6, 3.5), "text": "1. 重叠识别挑战\\n\\nTTS系统面临的首要挑战是识别出哪些文本是可见的、可读的。"},
        {"pos": (4, 3.5), "size": (6, 3), "text": "2. 层次顺序处理\\n\\n重叠文本通常具有明确的层次关系，位于上层的文本应该优先被读取。"},
        {"pos": (6, 4), "size": (6, 3), "text": "3. 透明度与可见性\\n\\n半透明文本层增加了识别难度，因为下层文本可能部分可见。"},
        {"pos": (8, 4.5), "size": (6, 2.5), "text": "4. 设计建议\\n\\n从可访问性角度考虑，应谨慎使用重叠布局。"}
    ]
    
    for layer in layers:
        text_box = slide.shapes.add_textbox(Inches(layer["pos"][0]), Inches(layer["pos"][1]), 
                                          Inches(layer["size"][0]), Inches(layer["size"][1]))
        text_frame = text_box.text_frame
        text_frame.text = layer["text"]
    
    # 4. 非线性阅读路径页
    slide_layout = prs.slide_layouts[5]
    slide = prs.slides.add_slide(slide_layout)
    
    # 标题
    title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(15), Inches(1))
    title_frame = title_box.text_frame
    title_frame.text = "非线性阅读路径"
    
    # 介绍文字
    intro_box = slide.shapes.add_textbox(Inches(1), Inches(1.5), Inches(14), Inches(1))
    intro_frame = intro_box.text_frame
    intro_frame.text = "非线性阅读路径打破了传统的阅读模式，内容以网络或图形的方式连接，阅读顺序可能跳跃或分支。"
    
    # 创建节点网络
    nodes = [
        {"pos": (2, 3), "text": "1. 起点\\n\\n非线性阅读路径通常有一个明确的起点"},
        {"pos": (6, 4), "text": "2. 分支点\\n\\n分支点是阅读路径分叉的位置"},
        {"pos": (12, 3), "text": "3. 路径A\\n\\n这是从分支点延伸出的一条路径"},
        {"pos": (2, 6), "text": "4. 路径B\\n\\n这是另一条从分支点延伸出的路径"},
        {"pos": (12, 6), "text": "5. 汇合点\\n\\n汇合点是多条路径会合的位置"},
        {"pos": (7, 7.5), "text": "6. 终点\\n\\n非线性路径通常有一个明确的终点"}
    ]
    
    for node in nodes:
        text_box = slide.shapes.add_textbox(Inches(node["pos"][0]), Inches(node["pos"][1]), 
                                          Inches(2), Inches(1.5))
        text_frame = text_box.text_frame
        text_frame.text = node["text"]
    
    # 保存文件
    output_path = "/home/<USER>/tts_ppt_files/TTS测试_复杂布局.pptx"
    prs.save(output_path)
    print(f"复杂布局PPT已保存: {output_path}")
    return output_path

def main():
    """生成所有PPT文件"""
    print("开始生成TTS测试PPT文件...")
    
    files = []
    files.append(create_simple_layout_ppt())
    files.append(create_medium_complexity_ppt())
    files.append(create_complex_layout_ppt())
    
    print(f"\\n所有PPT文件已生成完成！")
    print(f"输出目录: /home/<USER>/tts_ppt_files")
    for file in files:
        print(f"- {os.path.basename(file)}")
    
    return files

if __name__ == "__main__":
    main()

