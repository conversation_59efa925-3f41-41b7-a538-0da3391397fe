#!/home/<USER>/miniconda3/envs/sovit/bin/python
"""
YouTube Voice Translation with Open-Source Models

This script creates a pipeline to:
1. Download a YouTube video
2. Extract the audio
3. Transcribe the audio using Whisper
4. Translate the text to a target language
5. Generate speech in the target language
6. Optionally preserve voice characteristics
7. Combine the new audio with the original video
8. Adjust output audio volume as needed

Requirements:
- yt-dlp: For YouTube downloading
- whisper: For speech recognition
- transformers: For text translation
- TTS: For text-to-speech synthesis
- ffmpeg: For audio/video processing
- (Optional) RVC or So-VITS-SVC: For voice conversion

Usage:
python voice_translator.py --url <youtube_url> --target_language zh --volume 2.0"
"""

import argparse
import os
import sys
import tempfile
import time
import json
import subprocess
import shutil
from pathlib import Path

# Core dependencies - will be checked before use
try:
    import torch
    import numpy as np
    from scipy.io import wavfile
    import yt_dlp
    import whisper
    from transformers import AutoModelForSeq2SeqLM, AutoTokenizer
except ImportError:
    # These will be checked in check_packages()
    pass

# Required packages dictionary
required_packages = {
    "yt_dlp": ("yt-dlp", "YouTube video downloading"),
    "whisper": ("openai-whisper", "Speech recognition"),
    "transformers": ("transformers", "Text translation"),
    "TTS": ("TTS", "Text-to-speech synthesis"),
    "torch": ("torch", "PyTorch for deep learning"),
    "numpy": ("numpy", "Numerical computations"),
    "scipy": ("scipy", "Scientific computations"),
    "melo": ("git+https://github.com/myshell-ai/MeloTTS.git", "High-quality TTS")
}

# Optional packages that enhance functionality
optional_packages = {
    "gtts": ("gtts", "Google Text-to-Speech (fallback)"),
    "mecab": ("mecab-python3", "Japanese text processing"),
    "unidic": ("unidic-lite", "Japanese dictionary")
}

def check_packages():
    """Check required packages and provide installation advice if missing"""
    missing_packages = []
    missing_optional = []
    cuda_available = False
    
    print("\n=== Checking Required Packages ===")
    
    # Check CUDA first
    try:
        import torch
        if torch.cuda.is_available():
            cuda_available = True
            print(f"✓ CUDA available - GPU: {torch.cuda.get_device_name(0)}")
            print(f"  • Total Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️  CUDA not available - running in CPU mode (will be slower)")
            missing_optional.append(("cuda", "PyTorch CUDA support"))
    except ImportError:
        print("❌ PyTorch not installed")
        missing_packages.append(("torch", required_packages["torch"][0]))
    
    # Check core packages
    print("\n• Core Dependencies:")
    for module, (package, purpose) in required_packages.items():
        if module != "torch":  # Already checked above
            try:
                __import__(module)
                print(f"  ✓ {module:<12} - {purpose}")
                # If import succeeded, make it available globally
                if module not in globals():
                    globals()[module] = sys.modules[module]
            except ImportError:
                print(f"  ❌ {module:<12} - {purpose}")
                missing_packages.append((module, package))
    
    # Check optional packages
    print("\n• Optional Dependencies:")
    for module, (package, purpose) in optional_packages.items():
        try:
            __import__(module)
            print(f"  ✓ {module:<12} - {purpose}")
            # If import succeeded, make it available globally
            if module not in globals():
                globals()[module] = sys.modules[module]
        except ImportError:
            print(f"  ⚠️  {module:<12} - {purpose}")
            missing_optional.append((module, package))
    
    if missing_packages or missing_optional:
        if missing_packages:
            print("\n=== Required Packages Missing ===")
            print("Please run the following commands:")
            print("\n1. Activate conda environment:")
            print("   conda activate tools")
            print("\n2. Install missing packages:")
            print("   pip install", " ".join(pkg[1] for pkg in missing_packages))
        
        if missing_optional:
            print("\n=== Optional Packages Missing ===")
            print("For additional features, you can install:")
            print("\nconda activate tools")
            print("pip install", " ".join(pkg[1] for pkg in missing_optional))
            
            if ("cuda", "PyTorch CUDA support") in missing_optional:
                print("\nFor GPU acceleration (recommended):")
                print("1. Install NVIDIA GPU drivers")
                print("2. Install CUDA toolkit")
                print("3. Install PyTorch with CUDA:")
                print("   conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia")
        
        if missing_packages:  # Only exit if required packages are missing
            sys.exit(1)
    else:
        print("\n✓ All required packages are installed!")
        if cuda_available:
            print("✓ GPU acceleration is enabled!")
    
    # Return CUDA status for setup
    return cuda_available

def check_mecab():
    """Check MeCab installation and provide advice if missing"""
    print("\n=== Checking MeCab for Asian Language Support ===")
    
    mecab_status = {
        "python_mecab": False,
        "unidic": False,
        "system_mecab": False,
        "mecabrc": False
    }
    
    # Check Python MeCab
    try:
        import mecab
        print("✓ Python MeCab is installed")
        mecab_status["python_mecab"] = True
    except ImportError:
        print("❌ Python MeCab not installed")
    
    # Check unidic
    try:
        import unidic
        print("✓ Unidic dictionary is installed")
        mecab_status["unidic"] = True
        
        # Check unidic directory
        unidic_dir = unidic.DICDIR
        if os.path.exists(unidic_dir):
            print(f"  • Dictionary path: {unidic_dir}")
            
            # Check mecabrc
            mecabrc_path = os.path.join(unidic_dir, "mecabrc")
            if os.path.exists(mecabrc_path):
                print("  • MeCab configuration found")
                mecab_status["mecabrc"] = True
            else:
                print("  ❌ MeCab configuration missing")
        else:
            print(f"  ❌ Dictionary directory not found")
    except ImportError:
        print("❌ Unidic dictionary not installed")
    
    # Check system MeCab
    try:
        output = subprocess.check_output(["mecab", "--version"], stderr=subprocess.STDOUT).decode()
        version = output.split()[1]
        print(f"✓ System MeCab is installed (version {version})")
        mecab_status["system_mecab"] = True
    except (subprocess.SubprocessError, FileNotFoundError):
        print("❌ System MeCab not installed")
    
    if not all(mecab_status.values()):
        print("\n=== MeCab Installation Required ===")
        print("\nPlease follow these steps in order:")
        
        if not mecab_status["system_mecab"]:
            print("\n1. Install system MeCab:")
            print("   sudo apt-get update")
            print("   sudo apt-get install -y mecab libmecab-dev mecab-ipadic-utf8")
        
        if not mecab_status["python_mecab"] or not mecab_status["unidic"]:
            print("\n2. Install Python packages:")
            print("   conda activate tools")
            print("   pip install mecab-python3 unidic-lite")
        
        if not mecab_status["unidic"] or not mecab_status["mecabrc"]:
            print("\n3. Initialize unidic:")
            print("   python -m unidic download")
        
        print("\nAfter installation, restart your terminal and try again.")
        return False
    
    print("\n✓ MeCab is fully configured and ready to use!")
    return True

def check_ffmpeg():
    """Check if ffmpeg is installed and provide detailed information"""
    print("\n=== Checking FFmpeg Installation ===")
    
    try:
        # Check FFmpeg version
        output = subprocess.check_output(["ffmpeg", "-version"]).decode()
        version_line = output.split('\n')[0]
        version = version_line.split()[2]
        
        print(f"✓ FFmpeg is installed")
        print(f"  • Version: {version}")
        
        # Check FFmpeg codecs
        codecs_output = subprocess.check_output(["ffmpeg", "-codecs"], stderr=subprocess.PIPE).decode()
        
        # Check for important codecs
        important_codecs = {
            "libmp3lame": "MP3 encoding",
            "aac": "AAC audio",
            "pcm": "WAV audio",
            "h264": "H.264 video"
        }
        
        print("\n• Codec Support:")
        for codec, desc in important_codecs.items():
            if codec in codecs_output.lower():
                print(f"  ✓ {desc:<15} ({codec})")
            else:
                print(f"  ⚠️  {desc:<15} ({codec}) - not found")
        
        return True
        
    except (subprocess.SubprocessError, FileNotFoundError):
        print("\n❌ FFmpeg is not installed or not in PATH!")
        print("\nInstallation instructions:")
        print("\n• Windows:")
        print("  1. Download from https://ffmpeg.org/download.html")
        print("  2. Extract the archive")
        print("  3. Add the bin folder to your system PATH")
        print("\n• macOS:")
        print("  brew install ffmpeg")
        print("\n• Linux (Ubuntu/Debian):")
        print("  sudo apt-get update")
        print("  sudo apt-get install -y ffmpeg")
        print("\nAfter installation, restart your terminal and try again.")
        return False

def patch_tts_loading():
    """Patch TTS loading mechanism for PyTorch 2.6+ compatibility"""
    try:
        import importlib
        import types
        from TTS.utils import io
        import torch.serialization
        
        # Original load_fsspec function
        original_load_fsspec = io.load_fsspec
        
        # Create a patched version that uses weights_only=False
        def patched_load_fsspec(filepath, map_location="cpu", **kwargs):
            try:
                # First try with original function
                return original_load_fsspec(filepath, map_location=map_location, **kwargs)
            except Exception as e:
                if "weights_only" in str(e):
                    print("Attempting to load with weights_only=False...")
                    # If that fails, try with weights_only=False
                    with open(filepath, "rb") as f:
                        return torch.load(f, map_location=map_location, weights_only=False, **kwargs)
                else:
                    # If it's a different error, raise it
                    raise e
        
        # Replace the function
        io.load_fsspec = patched_load_fsspec
        
        # Try to add safe globals for known problematic classes
        try:
            from TTS.tts.configs.xtts_config import XttsConfig
            from TTS.tts.models.xtts import XttsAudioConfig
            torch.serialization.add_safe_globals([XttsConfig, XttsAudioConfig])
        except (ImportError, AttributeError):
            print("Could not add XttsConfig to safe globals")
        
        print("Successfully patched TTS loading for PyTorch 2.6+ compatibility")
        return True
    except Exception as e:
        print(f"Warning: Could not patch TTS loading mechanism: {e}")
        print("If you encounter loading errors, try downgrading PyTorch or updating TTS")
        return False

def patch_torch_load():
    """Patch torch.load to use weights_only=False by default for TTS models"""
    try:
        import torch
        import types
        
        # Save original torch.load
        original_torch_load = torch.load
        
        # Create a patched version that uses weights_only=False for TTS
        def patched_torch_load(f, map_location=None, pickle_module=None, 
                             weights_only=False, **pickle_load_args):
            # Check if this is a TTS model load
            if isinstance(f, str) and ("TTS" in f or "xtts" in f.lower() or "melo" in f.lower()):
                if weights_only is None or weights_only is True:
                    print("TTS/XTTS/Melo model detected, setting weights_only=False for compatibility")
                    weights_only = False
                    
            # Call original with potentially modified weights_only
            return original_torch_load(f, map_location=map_location, 
                                   pickle_module=pickle_module, 
                                   weights_only=weights_only, 
                                   **pickle_load_args)
        
        # Replace torch.load with our patched version
        torch.load = patched_torch_load
        print("Successfully patched torch.load to handle TTS models")
        return True
    except Exception as e:
        print(f"Warning: Could not patch torch.load: {e}")
        return False

def patch_weight_norm():
    """Patch torch.nn.utils.weight_norm to use parametrizations.weight_norm"""
    try:
        import torch
        import torch.nn as nn
        
        # Check if parametrizations is available (newer PyTorch)
        if hasattr(nn.utils, 'parametrizations') and hasattr(nn.utils.parametrizations, 'weight_norm'):
            # Use new implementation
            original_weight_norm = nn.utils.weight_norm
            nn.utils.weight_norm = nn.utils.parametrizations.weight_norm
            print("Successfully patched torch.nn.utils.weight_norm to use parametrizations.weight_norm")
        else:
            print("torch.nn.utils.parametrizations.weight_norm not available, skipping patch")
        
        return True
    except Exception as e:
        print(f"Warning: Could not patch weight_norm: {e}")
        return False

def patch_huggingface_download():
    """Patch HuggingFace's file_download.py to silence resume_download warnings"""
    try:
        # Try to patch huggingface_hub directly to suppress warnings
        import warnings
        import huggingface_hub
        
        # Use a filter to ignore the specific FutureWarning about resume_download
        warnings.filterwarnings(
            "ignore", 
            message=".*resume_download.* is deprecated", 
            category=FutureWarning
        )
        
        print("Successfully applied HuggingFace download warning filter")
        return True
    except Exception as e:
        print(f"Warning: Could not patch HuggingFace download: {e}")
        return False

# Install required packages
def load_tts():
    """Load TTS module when needed"""
    try:
        from TTS.api import TTS
        
        # Apply our patch immediately after importing TTS
        patch_success = patch_tts_loading()
        if patch_success:
            print("Successfully applied TTS loading patch")
        
        # Attempt to patch TTS loading again as a backup
        try:
            import torch.serialization
            import importlib
            
            # Try to import the config class if not already imported
            try:
                from TTS.tts.configs.xtts_config import XttsConfig
                from TTS.tts.models.xtts import XttsAudioConfig
                torch.serialization.add_safe_globals([XttsConfig, XttsAudioConfig])
            except ImportError:
                # If direct import fails, try alternative approach
                TTS_configs = importlib.import_module("TTS.tts.configs.xtts_config")
                XttsConfig = getattr(TTS_configs, "XttsConfig", None)
                TTS_models = importlib.import_module("TTS.tts.models.xtts")
                XttsAudioConfig = getattr(TTS_models, "XttsAudioConfig", None)
                if XttsConfig and XttsAudioConfig:
                    torch.serialization.add_safe_globals([XttsConfig, XttsAudioConfig])
        except (ImportError, AttributeError):
            print("Warning: Could not apply TTS patch for PyTorch 2.6+. Continuing anyway...")
            
        return TTS
    except ImportError:
        print("❌ TTS library not installed. Please install it using:")
        print("conda activate tools")
        print("pip install TTS")
        sys.exit(1)

def download_youtube_video(url, output_dir=None, audio_only=False):
    """
    Download a YouTube video and optionally extract audio
    """
    if output_dir is None:
        output_dir = tempfile.gettempdir()
    
    output_path = os.path.join(output_dir, "%(title)s.%(ext)s")
    
    if audio_only:
        ydl_opts = {
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'wav',
                'preferredquality': '192',
            }],
            'outtmpl': output_path,
            'quiet': False,
            'ignoreerrors': True,  # Continue on download errors
            'no_warnings': False,  # Show warnings
        }
    else:
        ydl_opts = {
            'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
            'outtmpl': output_path,
            'quiet': False,
            'ignoreerrors': True,  # Continue on download errors
            'no_warnings': False,  # Show warnings
        }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            print("Extracting video information...")
            info = ydl.extract_info(url, download=True)
            
            if not info:
                print("Error: Could not retrieve video information.")
                sys.exit(1)
                
            if audio_only:
                file_path = ydl.prepare_filename(info).rsplit(".", 1)[0] + ".wav"
            else:
                file_path = ydl.prepare_filename(info)
            
            # Verify file exists
            if not os.path.exists(file_path):
                print(f"Warning: Expected file {file_path} not found. Searching for similar files...")
                # Try to find a similar file in the output directory
                base_name = os.path.basename(file_path).split(".")[0]
                dir_name = os.path.dirname(file_path)
                for f in os.listdir(dir_name):
                    if base_name in f:
                        file_path = os.path.join(dir_name, f)
                        print(f"Found alternative file: {file_path}")
                        break
            
            return file_path, info.get('title', 'video')
    except Exception as e:
        print(f"Error downloading YouTube video: {e}")
        sys.exit(1)

def extract_audio(video_path, output_dir=None):
    """
    Extract audio from a video file
    """
    if output_dir is None:
        output_dir = os.path.dirname(video_path)
    
    audio_path = os.path.join(output_dir, os.path.basename(video_path).rsplit(".", 1)[0] + ".wav")
    
    cmd = [
        "ffmpeg", "-i", video_path, 
        "-vn", "-acodec", "pcm_s16le", 
        "-ar", "16000", "-ac", "1",
        audio_path, "-y"
    ]
    
    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return audio_path
    except subprocess.CalledProcessError as e:
        print(f"Error extracting audio: {e}")
        sys.exit(1)

def transcribe_audio(audio_file, model_name="medium", language=None):
    """
    Transcribe audio using OpenAI's Whisper model
    """
    print(f"Loading Whisper model '{model_name}'...")
    model = whisper.load_model(model_name)
    
    print(f"Transcribing audio file: {audio_file}")
    
    # Use language parameter if provided
    if language:
        result = model.transcribe(audio_file, language=language, verbose=False)
    else:
        result = model.transcribe(audio_file, verbose=False)
    
    # Extract transcript with timing info
    transcript = []
    for segment in result["segments"]:
        transcript.append({
            "start": segment["start"],
            "end": segment["end"],
            "text": segment["text"].strip()
        })
    
    return transcript, result["language"]

# Add CUDA check and setup
def setup_cuda():
    """Setup and verify CUDA availability"""
    if torch.cuda.is_available():
        device = "cuda"
        print(f"CUDA available. Using GPU: {torch.cuda.get_device_name(0)}")
        # Set default tensor type to CUDA - update with recommended approaches
        torch.set_default_dtype(torch.float32)
        torch.set_default_device('cuda')
    else:
        device = "cpu"
        print("CUDA not available. Using CPU.")
    return device

# Get global device
DEVICE = setup_cuda()

def translate_text(text, source_language, target_language):
    """
    Translate text using Hugging Face models
    """
    print(f"Translating text from {source_language} to {target_language}...")
    
    # Select the appropriate model based on language pair
    if source_language == target_language:
        return text
    
    # Convert language codes to NLLB format
    language_map = {
        "en": "eng_Latn",
        "fr": "fra_Latn",
        "es": "spa_Latn",
        "de": "deu_Latn",
        "it": "ita_Latn",
        "ja": "jpn_Jpan",
        "ko": "kor_Hang",
        "zh": "zho_Hans",
        "ru": "rus_Cyrl",
        "ar": "ara_Arab",
        "hi": "hin_Deva",
        "pt": "por_Latn",
        "nl": "nld_Latn",
        "tr": "tur_Latn",
        "pl": "pol_Latn",
        "vi": "vie_Latn",
        "th": "tha_Thai",
        "sv": "swe_Latn",
        "uk": "ukr_Cyrl",
        "cs": "ces_Latn",
        "el": "ell_Grek",
        "ro": "ron_Latn",
        "da": "dan_Latn",
        "fi": "fin_Latn",
        "id": "ind_Latn",
        "hu": "hun_Latn",
        "bn": "ben_Beng",
    }
    
    # NLLB is good for many language pairs
    model_name = "facebook/nllb-200-distilled-600M"
    
    # Load model and tokenizer
    print(f"Loading translation model: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(model_name, src_lang=language_map.get(source_language, "eng_Latn"))
    model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(DEVICE)
    
    source_lang = language_map.get(source_language, language_map.get("en"))
    target_lang = language_map.get(target_language, language_map.get("en"))
    
    # Process in batches to avoid running out of memory
    batch_size = 1024  # Characters per batch
    translated_segments = []
    
    # Define a helper function for translating batches
    def translate_batch(text_batch):
        inputs = tokenizer(text_batch, return_tensors="pt", padding=True)
        # Move inputs to GPU
        inputs = {k: v.to(DEVICE) for k, v in inputs.items()}
        
        # Set the target language token
        target_token_id = tokenizer.convert_tokens_to_ids(target_lang)
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                forced_bos_token_id=target_token_id,
                max_length=len(text_batch) + 100  # Allow for some expansion
            )
        
        return tokenizer.batch_decode(outputs, skip_special_tokens=True)[0]
    
    # Process each segment, possibly breaking up long ones
    for segment in text:
        original_text = segment["text"]
        
        # For short segments, translate directly
        if len(original_text) <= batch_size:
            translated_text = translate_batch(original_text)
        else:
            # For longer segments, break into sentences and translate
            # (This is a simplistic approach - better sentence splitting might be needed)
            sentences = original_text.split(". ")
            translated_sentences = []
            
            current_batch = ""
            for sentence in sentences:
                if len(current_batch) + len(sentence) <= batch_size:
                    if current_batch:
                        current_batch += ". " + sentence
                    else:
                        current_batch = sentence
                else:
                    # Translate current batch
                    if current_batch:
                        translated_sentences.append(translate_batch(current_batch))
                    current_batch = sentence
            
            # Translate any remaining text
            if current_batch:
                translated_sentences.append(translate_batch(current_batch))
            
            translated_text = " ".join(translated_sentences)
        
        # Create new segment with translated text
        translated_segments.append({
            "start": segment["start"],
            "end": segment["end"],
            "text": translated_text
        })
    
    return translated_segments

def generate_speech(text_segments, target_language, output_path, voice_preset=None):
    """
    Generate speech from translated text
    """
    # Check if the language is Japanese and try to ensure MeCab is installed
    if target_language in ["ja", "zh", "ko"]:
        print(f"Asian language detected ({target_language}), checking MeCab...")
        if not check_mecab():
            print("❌ MeCab is required for Asian languages. Please install it first.")
            sys.exit(1)
    
    # Try using MeloTTS first (high-quality multilingual TTS)
    try:
        print(f"Generating speech in {target_language} with MeloTTS...")
        try:
            from melo.api import TTS as MeloTTS
        except ImportError:
            print("❌ MeloTTS not installed. Please install it using:")
            print("conda activate tools")
            print("pip install git+https://github.com/myshell-ai/MeloTTS.git")
            sys.exit(1)
        
        # Map language codes to MeloTTS format
        melo_lang_map = {
            "en": "EN",
            "fr": "FR",
            "es": "ES",
            "zh": "ZH",
            "ja": "JP",
            "ko": "KR"
        }
        
        if target_language not in melo_lang_map:
            print(f"MeloTTS does not support language {target_language}, falling back...")
            raise ValueError(f"Unsupported language in MeloTTS: {target_language}")
        
        # Get language for MeloTTS
        melo_language = melo_lang_map[target_language]
        
        # Initialize MeloTTS model with CUDA if available
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"Using device for MeloTTS: {device}")
        model = MeloTTS(language=melo_language, device=device)
        speaker_ids = model.hps.data.spk2id
        
        # Select appropriate speaker based on language and voice_preset
        speaker_id = None
        
        if melo_language == "EN":
            # Select English speaker based on voice_preset
            if voice_preset == "male_01":
                speaker_id = speaker_ids.get('EN-US', speaker_ids.get('EN-Default'))
            elif voice_preset == "female_01":
                speaker_id = speaker_ids.get('EN-BR', speaker_ids.get('EN-Default'))
            else:
                speaker_id = speaker_ids.get('EN-Default', list(speaker_ids.values())[0])
        else:
            # For other languages, get the default speaker
            available_speakers = list(speaker_ids.keys())
            for spk in available_speakers:
                if melo_language in spk:
                    speaker_id = speaker_ids[spk]
                    break
            
            if speaker_id is None and available_speakers:
                speaker_id = speaker_ids[available_speakers[0]]
        
        # Create a directory for audio segments
        segments_dir = tempfile.mkdtemp(prefix="melo_segments_")
        segment_files = []
        
        # Speed setting
        speed = 1.0
        
        # Generate audio for each segment
        print("\nGenerating audio segments with MeloTTS...")
        for i, segment in enumerate(text_segments):
            segment_path = os.path.join(segments_dir, f"segment_{i:04d}.wav")
            print(f"\nProcessing segment {i+1}/{len(text_segments)}: {segment['text']}")
            
            try:
                model.tts_to_file(
                    segment["text"], 
                    speaker_id, 
                    segment_path, 
                    speed=speed
                )
                
                # Verify the generated file
                if not os.path.exists(segment_path) or os.path.getsize(segment_path) == 0:
                    raise RuntimeError(f"Generated audio file is empty or missing: {segment_path}")
                
                # Verify audio file integrity
                try:
                    subprocess.run(
                        ["ffmpeg", "-v", "error", "-i", segment_path, "-f", "null", "-"],
                        check=True, capture_output=True, text=True
                    )
                except subprocess.CalledProcessError as e:
                    print(f"Warning: Generated audio file may be corrupted: {e.stderr}")
                    raise RuntimeError("Audio file validation failed")
                
                segment_files.append({
                    "path": segment_path,
                    "start": segment["start"],
                    "end": segment["end"]
                })
                print(f"✓ Successfully generated segment {i+1}")
                
            except Exception as e:
                print(f"Error generating speech for segment {i}: {e}")
                raise
        
        print("\nAll segments generated successfully!")
        
        try:
            # Combine audio segments with correct timing
            combine_audio_segments(segment_files, output_path)
            print(f"\nMeloTTS successfully generated speech to: {output_path}")
            
        except Exception as e:
            print(f"\nError combining audio segments: {e}")
            raise
            
        finally:
            # Clean up temporary files
            try:
                shutil.rmtree(segments_dir)
            except Exception as e:
                print(f"Warning: Could not clean up temporary files: {e}")
        
        return output_path
        
    except Exception as melo_error:
        print(f"\nMeloTTS failed with error: {melo_error}")
        print("Attempting to use gTTS as fallback...")
        
        try:
            from gtts import gTTS
        except ImportError:
            print("❌ gTTS not installed. Please install it using:")
            print("conda activate tools")
            print("pip install gtts")
            sys.exit(1)
            
        # Map target language to gTTS format
        gtts_lang_map = {
            "en": "en",
            "fr": "fr",
            "es": "es",
            "de": "de",
            "it": "it",
            "ja": "ja",
            "ko": "ko",
            "zh": "zh-CN",
            "ru": "ru",
            "pt": "pt",
            "nl": "nl",
            "tr": "tr",
            "pl": "pl",
            "ar": "ar",
        }
        
        gtts_language = gtts_lang_map.get(target_language, "en")
        
        print(f"\nGenerating speech in {target_language} with gTTS...")
        
        # Create a directory for audio segments
        segments_dir = tempfile.mkdtemp(prefix="gtts_segments_")
        segment_files = []
        
        try:
            # Generate audio for each segment
            print("\nGenerating audio segments with gTTS...")
            for i, segment in enumerate(text_segments):
                print(f"\nProcessing segment {i+1}/{len(text_segments)}: {segment['text']}")
                segment_path = os.path.join(segments_dir, f"segment_{i:04d}.mp3")
                wav_path = os.path.join(segments_dir, f"segment_{i:04d}.wav")
                
                try:
                    # Generate MP3 with gTTS
                    tts = gTTS(text=segment["text"], lang=gtts_language, slow=False)
                    tts.save(segment_path)
                    
                    # Verify the MP3 file
                    if not os.path.exists(segment_path) or os.path.getsize(segment_path) == 0:
                        raise RuntimeError(f"Generated MP3 file is empty or missing: {segment_path}")
                    
                    # Convert MP3 to WAV
                    cmd = [
                        "ffmpeg", "-i", segment_path,
                        "-acodec", "pcm_s16le", 
                        "-ar", "22050", "-ac", "1",
                        wav_path, "-y"
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode != 0:
                        print("FFmpeg error converting MP3 to WAV:")
                        print(result.stderr)
                        raise RuntimeError("Failed to convert MP3 to WAV")
                    
                    # Verify the WAV file
                    if not os.path.exists(wav_path) or os.path.getsize(wav_path) == 0:
                        raise RuntimeError(f"Converted WAV file is empty or missing: {wav_path}")
                    
                    segment_files.append({
                        "path": wav_path,
                        "start": segment["start"],
                        "end": segment["end"]
                    })
                    print(f"✓ Successfully generated segment {i+1}")
                    
                except Exception as e:
                    print(f"Error processing segment {i}: {e}")
                    raise
            
            print("\nAll segments generated successfully!")
            
            # Combine audio segments with correct timing
            combine_audio_segments(segment_files, output_path)
            print(f"\ngTTS successfully generated speech to: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"\nError in gTTS processing: {e}")
            raise
            
        finally:
            # Clean up temporary files
            try:
                shutil.rmtree(segments_dir)
            except Exception as e:
                print(f"Warning: Could not clean up temporary files: {e}")
    
    return output_path

def combine_audio_segments(segment_files, output_path):
    """
    Combine audio segments with correct timing
    """
    try:
        # Create a temp file for the intermediate silent audio
        temp_audio = tempfile.mktemp(suffix=".wav")
        
        # Get the total duration from the last segment's end time
        total_duration = max([s["end"] for s in segment_files])
        
        # Create a silent audio file of the required duration
        silent_cmd = [
            "ffmpeg", "-f", "lavfi", 
            "-i", f"anullsrc=r=22050:cl=mono",  # Match sample rate with gTTS
            "-t", str(total_duration),
            "-c:a", "pcm_s16le",
            temp_audio, "-y"
        ]
        
        print("\nCreating base silent audio...")
        result = subprocess.run(silent_cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print("FFmpeg error creating silent audio:")
            print(result.stderr)
            raise RuntimeError("Failed to create silent audio base")
        
        # Create a complex filter to overlay all segments at the right timestamps
        filter_parts = []
        mix_parts = ["[0:a]"]  # Start with base silent audio
        
        # Add each segment to the filter
        for i, segment in enumerate(segment_files):
            delay_ms = int(segment["start"] * 1000)
            filter_parts.append(f"[{i+1}:a]adelay={delay_ms}|{delay_ms}[s{i}]")
            mix_parts.append(f"[s{i}]")
        
        # Add the final mix command
        filter_parts.append(f"{''.join(mix_parts)}amix=inputs={len(segment_files)+1}[aout]")
        
        # Build the complete filter
        filter_complex = ";".join(filter_parts)
        
        # Build the ffmpeg command
        cmd = ["ffmpeg", "-i", temp_audio]
        
        # Add input files
        for segment in segment_files:
            cmd.extend(["-i", segment["path"]])
        
        # Add filter and output options
        cmd.extend([
            "-filter_complex", filter_complex,
            "-map", "[aout]",
            "-c:a", "pcm_s16le",
            "-ar", "22050",  # Ensure consistent sample rate
            "-ac", "1",      # Ensure mono output
            output_path, "-y"
        ])
        
        print("\nCombining audio segments...")
        print("FFmpeg command:", " ".join(cmd))
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print("FFmpeg error combining segments:")
            print(result.stderr)
            raise RuntimeError("Failed to combine audio segments")
        
        # Clean up temp file
        os.remove(temp_audio)
        print("Successfully combined audio segments!")
        
    except Exception as e:
        print(f"\nError combining audio segments: {str(e)}")
        print("\nAttempting simplified combination method...")
        
        try:
            # Fallback to simple concatenation
            concat_list = tempfile.mktemp(suffix=".txt")
            with open(concat_list, "w", encoding="utf-8") as f:
                for segment in segment_files:
                    f.write(f"file '{segment['path']}'\n")
            
            # Simple concatenation command
            concat_cmd = [
                "ffmpeg", "-f", "concat",
                "-safe", "0",
                "-i", concat_list,
                "-c:a", "pcm_s16le",
                "-ar", "22050",
                "-ac", "1",
                output_path, "-y"
            ]
            
            print("Trying simple concatenation...")
            result = subprocess.run(concat_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print("FFmpeg error in simple concatenation:")
                print(result.stderr)
                raise RuntimeError("Failed to concatenate audio segments")
            
            os.remove(concat_list)
            print("Successfully concatenated audio segments!")
            
        except Exception as e2:
            print(f"\nBoth combination methods failed!")
            print(f"Original error: {str(e)}")
            print(f"Fallback error: {str(e2)}")
            raise RuntimeError("Could not combine audio segments")

def voice_conversion(source_audio, reference_audio, output_path):
    """
    Convert voice characteristics using optional RVC model
    NOTE: This requires additional setup and is left as a placeholder
    """
    print("NOTICE: Voice conversion functionality requires additional setup.")
    print("- RVC (Retrieval-based Voice Conversion): https://github.com/RVC-Project/Retrieval-based-Voice-Conversion-WebUI")
    print("- So-VITS-SVC: https://github.com/svc-develop-team/so-vits-svc")
    
    # For now, just copy the source audio
    shutil.copy(source_audio, output_path)
    
    return output_path

def combine_video_audio(video_path, audio_path, output_path, volume_boost=2.0):
    """
    Combine video with new audio track
    
    Args:
        video_path: Path to the input video file
        audio_path: Path to the input audio file
        output_path: Path to save the combined output video
        volume_boost: Factor to boost audio volume (e.g., 2.0 doubles the volume)
    """
    cmd = [
        "ffmpeg", 
        "-i", video_path,
        "-i", audio_path,
        "-c:v", "copy",       # Copy video stream without re-encoding
        "-c:a", "aac",        # Encode audio as AAC
        "-map", "0:v:0",      # Use the first video stream from the first input
        "-map", "1:a:0",      # Use the first audio stream from the second input
        "-af", f"volume={volume_boost}",  # Boost audio volume
        "-shortest",          # End when the shortest input stream ends
        output_path, "-y"
    ]
    
    subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    return output_path

def generate_srt(segments, output_path, is_translated=True):
    """
    Generate SRT subtitle file from segments
    """
    with open(output_path, "w", encoding="utf-8") as f:
        for i, segment in enumerate(segments, start=1):
            # Format start and end times as SRT timestamps (HH:MM:SS,mmm)
            start_time = format_timestamp(segment["start"])
            end_time = format_timestamp(segment["end"])
            
            # Write SRT entry
            f.write(f"{i}\n")
            f.write(f"{start_time} --> {end_time}\n")
            f.write(f"{segment['text']}\n\n")
    
    return output_path

def format_timestamp(seconds, always_include_hours=True):
    """
    Convert seconds to SRT timestamp format (HH:MM:SS,mmm)
    """
    hours = int(seconds / 3600)
    seconds %= 3600
    minutes = int(seconds / 60)
    seconds %= 60
    milliseconds = int(seconds * 1000) % 1000
    
    if always_include_hours or hours > 0:
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"
    else:
        return f"{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

def main():
    parser = argparse.ArgumentParser(description="YouTube Voice Translation with Open-Source Models")
    parser.add_argument("-u", "--url", help="YouTube video URL")
    parser.add_argument("--output_dir", help="Directory to save output files", default=".")
    parser.add_argument("--target_language", help="Target language code (e.g., 'fr' for French)", default="zh")
    parser.add_argument("--source_language", help="Source language code (detect automatically if not specified)")
    parser.add_argument("--whisper_model", help="Whisper model size", 
                        default="medium", choices=["tiny", "base", "small", "medium", "large"])
    parser.add_argument("--voice_preset", help="Voice preset to use", default="male_01", 
                        choices=["male_01", "female_01", "neural"])
    parser.add_argument("--keep_audio", help="Keep intermediate audio files", action="store_true")
    parser.add_argument("--voice_conversion", help="Apply voice conversion to preserve characteristics", 
                        action="store_true")
    parser.add_argument("--device", help="Device to use (cuda/cpu)", default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--volume", help="Volume boost factor for output audio (default: 2.0)", type=float, default=2.0)
    
    args = parser.parse_args()
    
    # Check requirements first
    print("\nChecking required packages...")
    check_packages()
    
    # Check ffmpeg
    if not check_ffmpeg():
        sys.exit(1)
    
    # Apply patches
    patch_torch_load()
    patch_weight_norm()
    patch_huggingface_download()
    
    # Setup CUDA
    global DEVICE
    DEVICE = setup_cuda() if args.device == "cuda" else "cpu"
    print(f"\nUsing device: {DEVICE}")
    
    # Check MeCab if needed for Asian languages
    if args.target_language in ["ja", "zh", "ko"]:
        if not check_mecab():
            sys.exit(1)
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    start_time = time.time()
    
    # Step 1: Download YouTube video
    print(f"Downloading video from: {args.url}")
    video_path, video_title = download_youtube_video(args.url, args.output_dir, audio_only=False)
    print(f"Download complete: {video_path}")
    
    # Step 2: Extract audio
    print("Extracting audio from video...")
    audio_path = extract_audio(video_path, args.output_dir)
    print(f"Audio extraction complete: {audio_path}")
    
    # Step 3: Transcribe audio
    print("Transcribing audio with Whisper...")
    transcript, detected_language = transcribe_audio(
        audio_path, 
        args.whisper_model,
        args.source_language
    )
    print(f"Transcription complete. Detected language: {detected_language}")
    
    # Create original subtitle file
    original_srt = os.path.join(args.output_dir, f"{video_title}_original.srt")
    generate_srt(transcript, original_srt, is_translated=False)
    print(f"Original language subtitles saved to: {original_srt}")
    
    # Step 4: Translate text
    if args.target_language != detected_language:
        print(f"Translating from {detected_language} to {args.target_language}...")
        translated_transcript = translate_text(transcript, detected_language, args.target_language)
        
        # Create translated subtitle file
        translated_srt = os.path.join(args.output_dir, f"{video_title}_{args.target_language}.srt")
        generate_srt(translated_transcript, translated_srt, is_translated=True)
        print(f"Translated subtitles saved to: {translated_srt}")
    else:
        print("Target language is the same as source language. Skipping translation.")
        translated_transcript = transcript
    
    # Step 5: Generate speech
    tts_audio_path = os.path.join(args.output_dir, f"{video_title}_{args.target_language}_tts.wav")
    print(f"Generating speech in {args.target_language}...")
    generate_speech(
        translated_transcript, 
        args.target_language, 
        tts_audio_path,
        args.voice_preset
    )
    print(f"Speech generation complete: {tts_audio_path}")
    
    # Step 6: Voice conversion (optional)
    if args.voice_conversion:
        print("Applying voice conversion to preserve original voice characteristics...")
        converted_audio_path = os.path.join(args.output_dir, f"{video_title}_{args.target_language}_converted.wav")
        voice_conversion(tts_audio_path, audio_path, converted_audio_path)
        final_audio_path = converted_audio_path
    else:
        final_audio_path = tts_audio_path
    
    # Step 7: Combine video with new audio
    output_video_path = os.path.join(args.output_dir, f"{video_title}_{args.target_language}.mp4")
    print("Combining video with translated audio...")
    print(f"Boosting audio volume by {args.volume}x...")
    combine_video_audio(video_path, final_audio_path, output_video_path, args.volume)
    
    # Clean up intermediate files if not keeping them
    if not args.keep_audio:
        intermediate_files = [tts_audio_path]
        if args.voice_conversion:
            intermediate_files.append(converted_audio_path)
        
        for file_path in intermediate_files:
            if os.path.exists(file_path):
                os.remove(file_path)
    
    elapsed_time = time.time() - start_time
    print(f"\nProcess completed in {elapsed_time:.2f} seconds!")
    print(f"Output video saved to: {output_video_path}")
    print(f"Output subtitles saved to: {os.path.join(args.output_dir, f'{video_title}_{args.target_language}.srt')}")

if __name__ == "__main__":
    main()
