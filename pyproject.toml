[project]
name = "readpal"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "accelerate>=1.8.1",
    "albumentations>=1.3.1",
    "cutlet>=0.5.0",
    "einops>=0.8.1",
    "en-core-web-sm",
    "espeak-phonemizer>=1.3.1",
    "f5-tts>=1.1.6",
    "flash-attn",
    "gradio>=5.35.0",
    "gtts>=2.5.4",
    "huggingface-hub>=0.33.2",
    "hydra-core>=1.3.2",
    "kokoro>=0.9.4",
    "kui>=1.9.2",
    "lightning>=2.5.2",
    "loguru>=0.7.2",
    "loralib>=0.1.2",
    "mecab-python3>=1.0.9",
    "melotts",
    "misaki[ja,zh]>=0.9.4",
    "munch>=4.0.0",
    "natsort>=8.4.0",
    "numpy<2",
    "opencv-python",
    "ormsgpack>=1.10.0",
    "paddleocr>=2.9.0",
    "paddlepaddle>=3.1.0",
    "pdf2image>=1.17.0",
    "phonemizer>=3.3.0",
    "pydub>=0.25.1",
    "pyrootutils>=1.0.4",
    "pytorch-lightning>=2.5.2",
    "requests>=2.32.4",
    "rich>=13.9.4",
    "scipy>=1.11.4",
    "soundfile>=0.13.1",
    "tiktoken>=0.9.0",
    "torch",
    "torchaudio",
    "torchvision",
    "transformers>=4.27.4",
    "unidic-lite>=1.0.8",
    "vector-quantize-pytorch>=1.22.17",
]

[tool.uv.sources]
torch = { path = "../../../install/jetpack6/v61/offcial/torch-2.8.0-cp310-cp310-linux_aarch64.whl" }
torchvision = { path = "../../../install/jetpack6/v61/offcial/torchvision-0.23.0-cp310-cp310-linux_aarch64.whl" }
torchaudio = { path = "../../../install/jetpack6/v61/offcial/torchaudio-2.8.0-cp310-cp310-linux_aarch64.whl" }
opencv-python = { path = "../../../install/jetpack6/v61/offcial/opencv_python-4.11.0-py3-none-any.whl" }
melotts = { git = "https://github.com/myshell-ai/MeloTTS.git" }
en-core-web-sm = { url = "https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl" }
flash-attn = { path = "../../../install/flash-attention/dist/flash_attn-2.6.3-cp310-cp310-linux_aarch64.whl" }
