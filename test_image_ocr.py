#!/usr/bin/env python3
"""
Test script for image-only OCR processing
Tests the core OCR and reading order logic without PPT/PDF conversion
"""

import os
import cv2
import numpy as np
import tempfile
import json
from pathlib import Path
from main import UbuntuDocumentTTSPipeline

def create_test_image(width=800, height=600):
    """Create a simple test image with text"""
    # Create white background
    img = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # Add some text using OpenCV
    font = cv2.FONT_HERSHEY_SIMPLEX
    
    # Title
    cv2.putText(img, "Test Document Title", (150, 80), font, 1.2, (0, 0, 0), 2)
    
    # Two column layout
    # Left column
    cv2.putText(img, "Left Column Header", (50, 150), font, 0.8, (0, 0, 0), 2)
    cv2.putText(img, "This is some text in", (50, 200), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "the left column of", (50, 230), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "the test document.", (50, 260), font, 0.6, (0, 0, 0), 1)
    
    # Right column
    cv2.putText(img, "Right Column Header", (450, 150), font, 0.8, (0, 0, 0), 2)
    cv2.putText(img, "This is some text in", (450, 200), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "the right column of", (450, 230), font, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "the test document.", (450, 260), font, 0.6, (0, 0, 0), 1)
    
    # Bottom section
    cv2.putText(img, "Bottom section spanning full width", (100, 350), font, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "This text should come after both columns.", (100, 400), font, 0.6, (0, 0, 0), 1)
    
    return img

def test_image_ocr_pipeline():
    """Test the OCR pipeline with image input"""
    
    print("🧪 Testing Image OCR Pipeline")
    print("=" * 50)
    
    # Create test image
    test_img = create_test_image()
    
    # Save test image
    test_image_path = "/tmp/test_ocr_image.jpg"
    cv2.imwrite(test_image_path, test_img)
    print(f"✅ Created test image: {test_image_path}")
    
    try:
        # Initialize pipeline (without TTS to focus on OCR)
        print("\n🔧 Initializing OCR pipeline...")
        pipeline = UbuntuDocumentTTSPipeline()
        
        # Test 1: OCR text extraction
        print("\n📝 Test 1: OCR Text Extraction")
        print("-" * 30)
        
        text_blocks = pipeline.extract_text_with_ocr(test_image_path)
        print(f"Found {len(text_blocks)} text blocks")
        
        for i, block in enumerate(text_blocks):
            print(f"Block {i+1}: '{block['text']}' (confidence: {block['confidence']:.2f})")
            print(f"  BBox: {block['bbox']}")
            print(f"  Area: {block['area']:.0f} pixels")
        
        # Test 2: Reading order detection
        print("\n🔄 Test 2: Reading Order Detection")
        print("-" * 30)
        
        image_shape = test_img.shape
        ordered_blocks = pipeline.detect_reading_order(text_blocks, image_shape)
        
        print("Reading order:")
        for i, block in enumerate(ordered_blocks):
            print(f"{i+1}. '{block['text']}'")
        
        # Test 3: Layout analysis
        print("\n📊 Test 3: Layout Analysis")
        print("-" * 30)
        
        width, height = image_shape[1], image_shape[0]
        
        is_title = pipeline._is_title_slide(text_blocks, width, height)
        is_two_col = pipeline._is_two_column_layout(text_blocks, width)
        
        print(f"Is title slide: {is_title}")
        print(f"Is two-column: {is_two_col}")
        
        # Test 4: Text combination
        print("\n📑 Test 4: Text Combination")
        print("-" * 30)
        
        # Add page info to blocks
        for block in ordered_blocks:
            block['page'] = 1
        
        combined_text = pipeline._combine_text_blocks(ordered_blocks)
        print("Combined text:")
        print(f"'{combined_text}'")
        
        # Test 5: Save debug image with bounding boxes
        print("\n🎨 Test 5: Debug Visualization")
        print("-" * 30)
        
        debug_img = test_img.copy()
        for i, block in enumerate(ordered_blocks):
            bbox = block['bbox']
            # Draw bounding box
            cv2.rectangle(debug_img, 
                         (int(bbox[0]), int(bbox[1])), 
                         (int(bbox[2]), int(bbox[3])), 
                         (0, 255, 0), 2)
            # Add order number
            cv2.putText(debug_img, str(i+1), 
                       (int(bbox[0]), int(bbox[1]-5)), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        debug_path = "/tmp/test_ocr_debug.jpg"
        cv2.imwrite(debug_path, debug_img)
        print(f"✅ Debug image saved: {debug_path}")
        
        # Test results summary
        print("\n📋 Test Results Summary")
        print("=" * 50)
        print(f"✅ OCR extraction: {len(text_blocks)} text blocks found")
        print(f"✅ Reading order: {len(ordered_blocks)} blocks ordered")
        print(f"✅ Layout detection: {'Two-column' if is_two_col else 'Single-column'}")
        print(f"✅ Text combination: {len(combined_text)} characters")
        print(f"✅ Debug visualization created")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_image(image_path):
    """Test with a real image file"""
    
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return False
    
    print(f"\n🖼️ Testing with real image: {image_path}")
    print("=" * 50)
    
    try:
        pipeline = UbuntuDocumentTTSPipeline()
        
        # Extract text
        text_blocks = pipeline.extract_text_with_ocr(image_path)
        
        # Detect reading order
        image = cv2.imread(image_path)
        ordered_blocks = pipeline.detect_reading_order(text_blocks, image.shape)
        
        # Add page info and combine text
        for block in ordered_blocks:
            block['page'] = 1
        combined_text = pipeline._combine_text_blocks(ordered_blocks)
        
        print(f"✅ Found {len(text_blocks)} text blocks")
        print(f"📖 Reading order determined")
        print(f"📝 Combined text ({len(combined_text)} chars):")
        print("-" * 30)
        print(combined_text)
        
        return True
        
    except Exception as e:
        print(f"❌ Real image test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_image_ocr_pipeline()
    
    # If you have a real image file, test with it too
    sample_images = [
        "/opt/app/tools/readpal/datafiles/img/MzE0JaigWdN6.jpg"
    ]
    
    print("\n🔍 Looking for sample images to test...")
    for img_path in sample_images:
        if os.path.exists(img_path):
            test_with_real_image(img_path)
            break
    else:
        print("💡 Place a sample image in /tmp/ to test with real content!")
    
    if success:
        print("\n🎉 All tests completed successfully!")
    else:
        print("\n❌ Some tests failed - check dependencies")