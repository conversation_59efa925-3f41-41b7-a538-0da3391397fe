2段組レイアウト左側のコンテンツ。右側のコンテンツ2段組レイアウトは一般的な方法で。通常右側のコラムは通常二次的なコンテンツ補足説明または関連図を配置するために使用されます。テストではシステムは見上げテストではこのレイアウトを使用してシステムが水平方向の読み上げ順序を正しく認識できるか検証できます。標準的な読み上げ順序はまず左側のコラムのすべてのコンテンツを読み次に右側のコラムのコンテンツを読むべきです。システムが読み上げ順序を正しく認識できるように明確な視覚的な区切り線異なる背景色または番号付きシステムをしレイアウトはシステムの空間認識能力に対してより高い要求をします。複雑なプレゼンテーションでは二段組レイアウトが他のレイアウト要素と混合して使用される場合がありこれはシステムの認識能力にとってより大きな課題となります。左側のコラムは通常主要なコンテンツや前提条件を配置するために使用され読み上げの開始位置です。設計時には左側のコンテンツと右側のコンテンツが明確に視覚的に区別されるようにする必要があります。システムがこの複雑な状況を正しく処理できるかどうかに注意を払う必要があります。