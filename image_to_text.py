#!/usr/bin/env python3
"""
Simple image-to-text processing function
Extracts text from images using the same OCR logic as main.py
"""

import os
import cv2
from pathlib import Path
from typing import List, Dict, Optional
from main import UbuntuDocumentTTSPipeline

class ImageToTextProcessor:
    """Simple wrapper for image-only text extraction"""
    
    def __init__(self):
        # Initialize only the OCR components (no TTS)
        self.pipeline = UbuntuDocumentTTSPipeline()
        print("✅ Image-to-text processor initialized")
    
    def process_image(self, image_path: str, output_text_path: Optional[str] = None) -> Dict:
        """
        Process a single image and extract text
        
        Args:
            image_path: Path to image file
            output_text_path: Optional path to save extracted text
            
        Returns:
            Dict with processing results
        """
        try:
            if not os.path.exists(image_path):
                raise Exception(f"Image not found: {image_path}")
            
            print(f"🔍 Processing image: {os.path.basename(image_path)}")
            
            # Extract text blocks
            text_blocks = self.pipeline.extract_text_with_ocr(image_path)
            
            # Detect reading order
            image = cv2.imread(image_path)
            if image is None:
                raise Exception(f"Could not load image: {image_path}")
            
            ordered_blocks = self.pipeline.detect_reading_order(text_blocks, image.shape)
            
            # Add page info (single page)
            for block in ordered_blocks:
                block['page'] = 1
            
            # Combine text
            combined_text = self.pipeline._combine_text_blocks(ordered_blocks)
            
            # Save text file if requested
            if output_text_path:
                with open(output_text_path, 'w', encoding='utf-8') as f:
                    f.write(combined_text)
                print(f"💾 Text saved to: {output_text_path}")
            
            # Analyze layout
            height, width = image.shape[:2]
            layout_info = {
                'is_title_slide': self.pipeline._is_title_slide(text_blocks, width, height),
                'is_two_column': self.pipeline._is_two_column_layout(text_blocks, width),
                'image_size': f"{width}x{height}",
                'total_blocks': len(text_blocks),
                'ordered_blocks': len(ordered_blocks)
            }
            
            results = {
                'success': True,
                'image_path': image_path,
                'text_blocks': len(text_blocks),
                'combined_text': combined_text,
                'layout_info': layout_info,
                'average_confidence': sum(b['confidence'] for b in text_blocks) / len(text_blocks) if text_blocks else 0,
                'detailed_blocks': ordered_blocks
            }
            
            return results
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'image_path': image_path
            }
    
    def process_multiple_images(self, image_folder: str, output_folder: Optional[str] = None) -> List[Dict]:
        """Process all images in a folder"""
        
        if not os.path.exists(image_folder):
            raise Exception(f"Folder not found: {image_folder}")
        
        # Find image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        
        for file_path in Path(image_folder).rglob('*'):
            if file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
        
        if not image_files:
            print(f"❌ No image files found in {image_folder}")
            return []
        
        print(f"📁 Found {len(image_files)} image files")
        
        # Process each image
        results = []
        for i, image_path in enumerate(image_files, 1):
            print(f"\n📄 Processing {i}/{len(image_files)}: {os.path.basename(image_path)}")
            
            # Set output path if folder provided
            output_text_path = None
            if output_folder:
                os.makedirs(output_folder, exist_ok=True)
                image_name = Path(image_path).stem
                output_text_path = os.path.join(output_folder, f"{image_name}.txt")
            
            result = self.process_image(image_path, output_text_path)
            results.append(result)
            
            if result['success']:
                print(f"✅ Text extracted: {len(result['combined_text'])} characters")
            else:
                print(f"❌ Failed: {result['error']}")
        
        return results


def main():
    """Command line interface"""
    import sys
    
    if len(sys.argv) < 2:
        print("""
🖼️ Image to Text Converter
Usage:
    python image_to_text.py <image_path> [output_text_path]
    python image_to_text.py <folder_path> [output_folder]

Examples:
    python image_to_text.py photo.jpg
    python image_to_text.py photo.jpg extracted_text.txt
    python image_to_text.py images/ text_output/
        """)
        return
    
    input_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        processor = ImageToTextProcessor()
        
        if os.path.isfile(input_path):
            # Single image
            result = processor.process_image(input_path, output_path)
            
            if result['success']:
                print("\n📋 Results:")
                print(f"✅ Text blocks found: {result['text_blocks']}")
                print(f"📊 Average confidence: {result['average_confidence']:.2f}")
                print(f"🏗️ Layout: {'Two-column' if result['layout_info']['is_two_column'] else 'Single-column'}")
                print(f"📄 Extracted text ({len(result['combined_text'])} chars):")
                print("-" * 40)
                print(result['combined_text'])
            else:
                print(f"❌ Processing failed: {result['error']}")
        
        elif os.path.isdir(input_path):
            # Multiple images
            results = processor.process_multiple_images(input_path, output_path)
            
            successful = [r for r in results if r['success']]
            failed = [r for r in results if not r['success']]
            
            print(f"\n📊 Batch Processing Results:")
            print(f"✅ Successful: {len(successful)}")
            print(f"❌ Failed: {len(failed)}")
            
            if successful:
                total_chars = sum(len(r['combined_text']) for r in successful)
                avg_confidence = sum(r['average_confidence'] for r in successful) / len(successful)
                print(f"📝 Total text extracted: {total_chars} characters")
                print(f"📊 Average confidence: {avg_confidence:.2f}")
        
        else:
            print(f"❌ Path not found: {input_path}")
    
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()